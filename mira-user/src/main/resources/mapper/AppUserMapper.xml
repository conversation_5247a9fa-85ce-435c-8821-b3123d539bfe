<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">


<mapper namespace="com.mira.user.dal.mapper.AppUserMapper">
    <delete id="deleteByUserId">
        delete
        from app_user
        where id = #{userId}
    </delete>


    <select id="searchDeskUserInfos" resultType="com.mira.api.user.dto.user.DeskSearchUserDTO">
        select u.id,
               u.email,
               u.status,
               u.mobile,
               u.source,
               u.transfer_flag,
               u.deleted,
               i.bind_device,
               concat('E3', upper(right(i.bind_device, 8))) as 'sn',
               IFNULL(p.fresh, 0)                           as 'fresh'
        from app_user u
                 left join app_user_info i on u.id = i.user_id
                 LEFT JOIN app_user_period p on u.id = p.user_id
        where u.email like concat('%', #{keyword,jdbcType=VARCHAR}, '%')
           or u.id = #{keyword}
           or concat('E3', upper(right(i.bind_device, 8))) = #{keyword}
    </select>

    <select id="getDeskUserInfoDTO" resultType="com.mira.api.user.dto.user.DeskUserInfoDTO">
        SELECT u.id,
               u.email,
               u.source,
               u.status,
               u.transfer_flag,
               ui.create_time_str as 'createTime', ui.time_zone as 'timeZone', ui.nickname,
               ui.birth_year,
               ui.birth_month,
               ui.birth_of_day as 'birth_day', ui.goal_status,
               ui.bind_version,
               IF(ui.bind_device = '', NULL, CONCAT('E3', upper(SUBSTRING(ui.bind_device, -8)))) as sn,
               ui.defined_irregular_cycle,
               ui.tracking_menopause,
               ui.bind_flag,
               up.avg_len_cycle as 'avgLenCycle',
               up.avg_len_period as 'avgLenPeriod',
               up.fresh as 'fresh',
               up.cycle_flag as 'cycle_flag',
               up.period_flag as 'period_flag',
               up.switch_flag as 'period_flag_switch',
               pt.email as 'partner_email',
               pt.status as 'partner_status',
               pt.deleted as 'partner_deleted',
               bt.bind_status as 'bbt_bind_status',
               bt.create_time_str as 'bbt_bind_create_time'
        from app_user u
                 LEFT JOIN app_user_info ui on u.id = ui.user_id
                 LEFT JOIN app_user_period up on u.id = up.user_id
                 LEFT JOIN app_user_partner pt on pt.user_id = u.id
                 LEFT JOIN app_user_bbt_bind bt on bt.user_id = u.id
        where u.id = #{userId}
    </select>

    <select id="getSurveyUserInfoDTO" resultType="com.mira.user.dto.survey.SurveyUserInfoDTO">
        select i.user_id,
               i.birth_year,
               i.birth_month,
               i.birth_of_day                  as 'birth_day',
               i.goal_status,
               i.conditions,
               i.bind_device,
               i.tracking_menopause,
               u.time_zone,
               u.ip_modify_time_str,
               u.country_code,
               u.continent_code,
               u.current_currency,
               r.cycle_data,
               r.hormone_data,
               if(s.user_id is not null, 1, 0) as subscription,
               if(o.user_id is not null, 1, 0) as coaching
        from app_user_info i
                 LEFT JOIN app_user u on i.user_id = u.id
                 join app_user_algorithm_result r on i.user_id = r.user_id
                 left join admin_notification_subscription s on i.user_id = s.user_id
                 left join admin_notification_coaching o on i.user_id = o.user_id
        where i.user_id = #{userId}
    </select>
</mapper>
