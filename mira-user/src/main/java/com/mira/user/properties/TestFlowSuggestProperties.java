package com.mira.user.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * test flow suggest properties
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Component
@RefreshScope
public class TestFlowSuggestProperties {
    /**
     * 图片 RECOMMENDED_1_TEST MAX
     */
    @Value("${test.flow.recommended_1_test_image.max}")
    private String recommended_1_test_image_max;

    /**
     * 图片 RECOMMENDED_1_TEST PLUS
     */
    @Value("${test.flow.recommended_1_test_image.plus}")
    private String recommended_1_test_image_plus;

    /**
     * 图片 RECOMMENDED_1_TEST PDG
     */
    @Value("${test.flow.recommended_1_test_image.pdg}")
    private String recommended_1_test_image_pdg;

    /**
     * 图片 RECOMMENDED_1_TEST FSH
     */
    @Value("${test.flow.recommended_1_test_image.fsh}")
    private String recommended_1_test_image_fsh;

    /**
     * 图片 RECOMMENDED_MULTIPLE_NO_TESTS
     */
    @Value("${test.flow.recommended_multiple_no_tests_image}")
    private String recommended_multiple_no_tests_image;

    /**
     * 图片 RECOMMENDED_MULTIPLE_PART_TESTS
     */
    @Value("${test.flow.recommended_multiple_part_tests_image}")
    private String recommended_multiple_part_tests_image;

    /**
     * 图片 ALL_RECOMMENDED_TESTS_TAKEN
     */
    @Value("${test.flow.all_recommended_tests_taken_image}")
    private String all_recommended_tests_taken_image;

    /**
     * 图片 NO_RECOMMENDED_TESTS
     */
    @Value("${test.flow.no_recommended_tests_image}")
    private String no_recommended_tests_image;
}
