package com.mira.user.enums.chart;

import lombok.Getter;

/**
 * 试剂产品名
 *
 * <AUTHOR>
 */
@Getter
public enum ProductNameEnum {
    HCG("02", "Pregnancy Wand"),
    PLUS("03", "Plus Wand"),
    PDG("09", "Confirm Wand"),
    MAX("12", "Max Wand"),
    HCG_QUALITATIVE("14", "Pregnancy Wand"),
    FSH("16", "Ovum Wand")
    ;

    private final String productCode;
    private final String wandName;

    ProductNameEnum(String productCode, String wandName) {
        this.productCode = productCode;
        this.wandName = wandName;
    }

    public static ProductNameEnum get(String productCode) {
        for (ProductNameEnum productNameEnum : ProductNameEnum.values()) {
            if (productNameEnum.getProductCode().equals(productCode)) {
                return productNameEnum;
            }
        }
        return null;
    }
}
