package com.mira.user.enums.home;

import lombok.Getter;

@Getter
public enum HomeIntroduceEventEnum {
    // -------------------- fertile window --------------------
    FERTILE_WINDOW_PREDICTED(
            HomeIntroduceTagEnum.NEW,
            "Fertile window predicted",
            "Get ready…your fertile window’s coming! \uD83D\uDD25 Ready to catch that peak?",
            HomeIntroduceNoteEnum.CYCLE
    ),
    FERTILE_WINDOW_STARTED(
            HomeIntroduceTagEnum.IMPORTANT,
            "Fertile window started",
            "This is it \uD83D\uDD25 Your most fertile days are here—your best chance to conceive. You’ve got this.",
            HomeIntroduceNoteEnum.CYCLE
    ),
    FERTILE_WINDOW_DELAY(
            HomeIntroduceTagEnum.NEW,
            "Fertile window delayed",
            "Your fertile window’s running late. That’s completely normal—cycles can shift for lots of reasons. Let’s look out for changes \uD83D\uDC40",
            HomeIntroduceNoteEnum.TEST
    ),
    FERTILE_WINDOW_STARTED_EARLIER_THAN_EXPECTED(
            HomeIntroduceTagEnum.NEW,
            "Fertile window started earlier than expected",
            "Oh! \uD83D\uDC83 Your fertile window’s here earlier than usual. Now’s your moment—make the most of it!",
            HomeIntroduceNoteEnum.TEST
    ),
    FERTILE_WINDOW_ENDS_IN_2_DAYS(
            HomeIntroduceTagEnum.IMPORTANT,
            "Fertile window ends in 2 days",
            "2 days left in your fertile window. Keep trying—there’s still time! ✨",
            HomeIntroduceNoteEnum.CYCLE
    ),
    FERTILE_WINDOW_ENDED(
            HomeIntroduceTagEnum.NEW,
            "Fertile window ended",
            "Your fertile window’s closed—congrats on tracking like a pro \uD83D\uDC51",
            HomeIntroduceNoteEnum.CYCLE
    ),

    // -------------------- ovulation --------------------
    LH_PEAK_DETECTED(
            HomeIntroduceTagEnum.NEW,
            "LH peak",
            "Your LH is surging—happy peak day! \uD83D\uDE4C Ovulation is likely coming within the next 24-36 hours. Stick to your plan and keep trying.",
            HomeIntroduceNoteEnum.TEST
    ),
    OVULATION_PREDICTED(
            HomeIntroduceTagEnum.IMPORTANT,
            "Ovulation predicted",
            "Ovulation is on its way—and you’re doing amazing with testing \uD83D\uDD25 Let’s catch those fertile days.",
            HomeIntroduceNoteEnum.CYCLE
    ),
    OVULATION_DETECTED(
            HomeIntroduceTagEnum.NEW,
            "Ovulation detected",
            "\uD83D\uDDE3\uFE0F Your LH levels are saying ovulation is HERE! Tracking will confirm it—so exciting!",
            HomeIntroduceNoteEnum.TEST
    ),
    OVULATION_NOT_DETECTED(
            HomeIntroduceTagEnum.UPDATE,
            "Ovulation not detected",
            "Hey \uD83D\uDC9A We didn’t detect ovulation this cycle, based on your LH and E3G. We’ve got you, though—we’ll watch your patterns.",
            HomeIntroduceNoteEnum.TEST
    ),
    OVULATION_CONFIRMED(
            HomeIntroduceTagEnum.NEW,
            "Ovulation confirmed",
            "Amazing news! Ovulation is confirmed \uD83D\uDE0D Your PdG levels made it official, and you absolutely deserve to celebrate.",
            HomeIntroduceNoteEnum.TEST
    ),
    OVULATION_IS_NOT_CONFIRMED(
            HomeIntroduceTagEnum.NEW,
            "Ovulation is not confirmed",
            "Your PdG didn’t confirm ovulation. Don’t worry, anovulation can happen for different reasons. You’re doing great—we’ll support you through your next cycle.",
            HomeIntroduceNoteEnum.TEST
    ),

    // -------------------- period --------------------
    PERIOD_IS_LATE_FOR_2_DAYS(
            HomeIntroduceTagEnum.NEW,
            "Your period is 2 days late",
            "Your period hasn’t started yet. It’s 2 days later than expected—be kind to yourself as you wait \uD83D\uDC96",
            HomeIntroduceNoteEnum.LOG
    ),
    PERIOD_IS_LATE_FOR_3_DAYS(
            HomeIntroduceTagEnum.NEW,
            "Your period is 3 days late",
            "Your period is 3 days late. Keep tracking, consider a pregnancy test if it feels right, and remember to log how you’re feeling \uD83D\uDC96",
            HomeIntroduceNoteEnum.LOG
    ),
    PERIOD_IS_LATE_FOR_4_DAYS(
            HomeIntroduceTagEnum.NEW,
            "Your period is 4 days late",
            "Your period is 4 days late. This could be an early sign of pregnancy—consider testing when you feel ready \uD83D\uDC96\n",
            HomeIntroduceNoteEnum.LOG
    ),
    PERIOD_IS_LATE_FOR_5_DAYS(
            HomeIntroduceTagEnum.NEW,
            "Your period is 5 days late",
            "Your period is 5 days late. If you haven’t tested for pregnancy yet, now might be the time.",
            HomeIntroduceNoteEnum.LOG
    ),
    PERIOD_IS_LATE_FOR_6_PLUS_DAYS(
            HomeIntroduceTagEnum.IMPORTANT,
            "It’s time to take a pregnancy test",
            "Okay, this is the right moment to take a pregnancy test. Choose a sensitive one and take your time with the instructions.",
            HomeIntroduceNoteEnum.LOG
    ),

    // -------------------- app behaviour --------------------
    USER_TESTING_SCHEDULE_IS_CHANGED(
            HomeIntroduceTagEnum.NEW,
            "Your testing schedule has changed",
            "Hey \uD83D\uDC4B We’ve noticed a change in your hormone patterns. Follow your new testing plan for more accurate cycle results.",
            HomeIntroduceNoteEnum.TEST
    ),
    USER_PREDICTIONS_ARE_UPDATED(
            HomeIntroduceTagEnum.NEW,
            "Your predictions have been updated",
            "Wowww, your hormones are doing their thing! We’ve updated your cycle predictions to keep you in tune with your patterns.",
            HomeIntroduceNoteEnum.CYCLE
    )
    ;

    private final HomeIntroduceTagEnum tag;
    private final String title;
    private final String body;
    private final HomeIntroduceNoteEnum note;

    HomeIntroduceEventEnum(HomeIntroduceTagEnum tag, String title, String body, HomeIntroduceNoteEnum note) {
        this.tag = tag;
        this.title = title;
        this.body = body;
        this.note = note;
    }
}
