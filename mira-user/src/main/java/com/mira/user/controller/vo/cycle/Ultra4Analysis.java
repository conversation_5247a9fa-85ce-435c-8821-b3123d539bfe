package com.mira.user.controller.vo.cycle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class Ultra4Analysis {
    @ApiModelProperty("1: <21 days - short; " +
            "0: 21—35 days - normal; " +
            "2: >35 days - long; " +
            "-1: NA")
    private Integer cycleLenV2;

    @ApiModelProperty("1: Irregular; " +
            "0: Regular; " +
            "-1: NA")
    private Integer cycleLenVarV2;

    @ApiModelProperty("0: Short; " +
            "1: Normal; " +
            "3: Long; " +
            "-1: NA")
    private Integer periodLenV2;

    @ApiModelProperty("黄体期长度平均值_v2")
    private Integer lutealPhasesV2;

    @ApiModelProperty("月经结束到排卵长度平均值_v2")
    private Integer folicularPhasesV2;

    @ApiModelProperty("排卵日Index平均值_v2")
    private Integer ovulationEstimateV2;

    @ApiModelProperty("排卵日Index单位_v2")
    private String ovulationEstimateUnitV2;

    @ApiModelProperty("卵巢储备_v2: " +
            "1-Normal; " +
            "0-Low; " +
            "-1-NA")
    private Integer ovarianReserveV2;

    @ApiModelProperty("cycle_history_analysis")
    private List<HistoryAnalysis> cycleHistoryAnalysis = new ArrayList<>();

    @Getter
    @Setter
    public static class HistoryAnalysis {
        @ApiModelProperty("Cycle Index")
        private Integer cycleIndex;

        @ApiModelProperty("周期概述_v2: " +
                "-1-NA; " +
                "0-LH surge + PdG rise + E3G is within the range; " +
                "1-LH surge + PdG rise + E3G is outside the range; " +
                "2-No LH surge, but PdG rise; " +
                "3-LH surge, but no PdG rise; " +
                "4-no LH surge; no PdG rise, E3G is within range")
        private Integer cycleOverviewV2;

        @ApiModelProperty("e3g_max_v2")
        private Double e3gMaxV2;

        @ApiModelProperty("e3g_min_v2")
        private Double e3gMinV2;

        @ApiModelProperty("e3g_folicular_phase_v2: " +
                "-1-NA; " +
                "0-Within the range; " +
                "1-Higher; " +
                "2-Lower")
        private Integer e3gFolicularPhaseV2;

        @ApiModelProperty("e3g_luteal_phases_v2: " +
                "-1-NA; " +
                "0-Within the range; " +
                "1-Higher; " +
                "2-Lower")
        private Integer e3gLutealPhasesV2;

        @ApiModelProperty("e3g_no_period_v2: " +
                "-1-NA; " +
                "0-Within the range; " +
                "1-Higher; " +
                "2-Lower")
        private Double e3gNoPeriodV2;

        @ApiModelProperty("pdg_max_v2")
        private Double pdgMaxV2;

        @ApiModelProperty("pdg_min_v2")
        private Double pdgMinV2;

        @ApiModelProperty("pdg_rise_v2: " +
                "-1-NA; " +
                "0-Rise not detected; " +
                "1-Rise is detected")
        private Integer pdgRiseV2;

        @ApiModelProperty("lh_max_v2")
        private Double lhMaxV2;

        @ApiModelProperty("lh_min_v2")
        private Double lhMinV2;

        @ApiModelProperty("lh_surge_v2: " +
                "-1-NA; " +
                "0-LH Surge not detected; " +
                "1-Surge is detected; " +
                "2-Additional LH Pattern")
        private Integer lhSurgeV2;

        @ApiModelProperty("fsh_max_v2")
        private Double fshMaxV2;

        @ApiModelProperty("fsh_min_v2")
        private Double fshMinV2;

        @ApiModelProperty("e3g_pdg_coordination_v2: " +
                "-1-NA; " +
                "0-Normal E3G, PdG rise; " +
                "1-Higher E3G, PdG rise; " +
                "2-Higher E3G, no PdG rise")
        private Integer e3gPdgCoordinationV2;

        @ApiModelProperty("e3g_lh_coordination_v2: " +
                "-1-NA; " +
                "0-LH surge, no E3G rise; " +
                "1-LH surge, E3G rise")
        private Integer e3gLhCoordinationV2;

        @ApiModelProperty("fsh_lh_ratio_coordination_v2: " +
                "-1: NA; " +
                "0: Good ratio; " +
                "1: Too high ratio")
        private Integer fshLhRatioCoordinationV2;

        @ApiModelProperty("fsh_lh_ovu_coordination_v2: " +
                "0: Normal pattern: LH and FSH surge together or FSH rises 1 day before or after LH; " +
                "1: Wrong pattern: LH surge without FSH surge; " +
                "2: Wrong pattern: Ongoing elevated FSH; " +
                "3: Wrong pattern: FSH changes without an LH surge")
        private Integer fshLhOvuCoordinationV2;
    }

}
