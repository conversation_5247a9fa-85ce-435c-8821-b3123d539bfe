package com.mira.user.controller.common;

import com.mira.api.bluetooth.dto.hormone.ManualDataDTO;
import com.mira.user.controller.vo.common.CommonConfigVO;
import com.mira.user.service.common.ICommonService;
import com.mira.user.service.hormone.IHormoneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 通用控制器
 *
 * <AUTHOR>
 */
@Api(tags = "51.通用配置")
@RestController
@RequestMapping("/app/v4")
public class CommonController {
    @Resource
    private ICommonService commonService;
    @Resource
    private IHormoneService hormoneService;

    @ApiOperation("获取通用配置")
    @GetMapping("/h5/common-config")
    public CommonConfigVO commonConfig() {
        return commonService.commonConfig();
    }

    @Deprecated
    @ApiOperation("添加测试数据")
    @PostMapping("/manual-data/add")
    public void manualAddData(@RequestBody ManualDataDTO manualDataDTO) {
        hormoneService.manualAddData(manualDataDTO);
    }

    @ApiOperation("校验接口，前端用")
    @GetMapping("/check")
    public String check() {
        return "success";
    }
}
