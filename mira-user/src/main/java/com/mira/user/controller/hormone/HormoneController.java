package com.mira.user.controller.hormone;

import com.mira.api.bluetooth.dto.hormone.DataHistoryDTO;
import com.mira.api.bluetooth.dto.hormone.TestDateDTO;
import com.mira.core.annotation.Idempotent;
import com.mira.core.request.PageDTO;
import com.mira.mybatis.response.PageResult;
import com.mira.user.controller.vo.hormone.UserDataManualVO;
import com.mira.user.dto.wand.WandCountDTO;
import com.mira.api.bluetooth.dto.hormone.ManualDataDTO;
import com.mira.user.service.hormone.IAppUserWandCountService;
import com.mira.user.service.hormone.IHormoneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 荷尔蒙测试数据
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-07-31
 **/
@Api(tags = "04.荷尔蒙数据")
@RestController
@RequestMapping("/app/v4/hormone")
public class HormoneController {
    @Resource
    private IHormoneService hormoneService;
    @Resource
    private IAppUserWandCountService appUserWandCountService;

    @ApiOperation("1.添加测试数据")
    @PostMapping("/manual-data/add")
    public void manualAddData(@RequestBody ManualDataDTO manualDataDTO) {
        hormoneService.manualAddData(manualDataDTO);
    }

    @ApiOperation("2.用户测试数据分页列表")
    @PostMapping("/manual-data-page")
    public PageResult<UserDataManualVO> manualDataPage(@RequestBody PageDTO pageParam) {
        return hormoneService.manualDataPage(pageParam);
    }

    @ApiOperation("3.获取某一天的测试历史")
    @GetMapping("/data/test-history")
    public DataHistoryDTO getHistory(@RequestParam String dateStr) {
        return hormoneService.getHistory(dateStr);
    }

    @ApiOperation("4.获取Which Wands do you have?")
    @GetMapping("/wand/count")
    public WandCountDTO wandCount() {
        return appUserWandCountService.wandCount();
    }

    @ApiOperation("5.set Which Wands do you have?")
    @PostMapping("/wand/count/set")
    @Idempotent
    public void setWandCount(@RequestBody WandCountDTO wandCountDTO) {
        appUserWandCountService.setWandCount(wandCountDTO);
    }

    @ApiOperation("6.获取下一个测试日")
    @GetMapping("/next-test-date")
    public TestDateDTO getNextTestDate() {
        return hormoneService.getNextTestDate();
    }


}
