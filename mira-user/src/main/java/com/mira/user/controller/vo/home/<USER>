package com.mira.user.controller.vo.home;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@ApiModel("Home Introduce")
public class HomeIntroduceVO {
    @ApiModelProperty("tag")
    private Tag tag;

    @ApiModelProperty("title")
    private String title;

    @ApiModelProperty("body")
    private String body;

    @ApiModelProperty("note")
    private Note note;

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Tag {
        @ApiModelProperty("id")
        private Integer id;

        @ApiModelProperty("name")
        private String name;
    }

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Note {
        @ApiModelProperty("id")
        private Integer id;

        @ApiModelProperty("content")
        private String content;
    }
}
