package com.mira.user.controller.front;

import com.mira.user.controller.vo.home.HomeIntroduceVO;
import com.mira.user.controller.vo.home.HomeV5CycleDataVO;
import com.mira.user.controller.vo.home.HomeV5DailyDataVO;
import com.mira.user.controller.vo.menopause.TestingPlanDetailVO;
import com.mira.user.enums.home.HomeActionButtonCodeEnum;
import com.mira.user.service.front.IHomeV5Service;
import com.mira.user.service.menopause.IMenopauseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-12-13
 **/
@Api(tags = "10.首页v5")
@RestController
@RequestMapping("/app/v5")
public class V5HomeController {
    @Resource
    private IHomeV5Service homeV5Service;
    @Resource
    private IMenopauseService menopauseService;


    @ApiOperation("V5首页周期数据")
    @GetMapping("/home/<USER>")
    public HomeV5CycleDataVO homeCycleData(@RequestParam(required = false) Integer cycleIndex) {
        return homeV5Service.homeCycleData(cycleIndex);
    }

    @ApiOperation("V5首页每日数据")
    @GetMapping("/home/<USER>")
    public HomeV5DailyDataVO homeDailyData(@RequestParam(required = false) String date) {
        return homeV5Service.homeDailyData(date);
    }

    @ApiOperation("set action button")
    @GetMapping("/action-button")
    public void cacheActionButton(@RequestParam(name = "selectCode") HomeActionButtonCodeEnum actionButtonCodeEnum) {
        homeV5Service.cacheActionButton(actionButtonCodeEnum);
    }

    @ApiOperation(value = "5. 获取首页的测试计划详情")
    @GetMapping("/home/<USER>/detail")
    public TestingPlanDetailVO testingPlanDetail() {
        return menopauseService.testingPlanDetail();
    }

    @ApiOperation("Home Introduce")
    @GetMapping("/home/<USER>")
    public HomeIntroduceVO introduce() {
        return homeV5Service.introduce();
    }
}
