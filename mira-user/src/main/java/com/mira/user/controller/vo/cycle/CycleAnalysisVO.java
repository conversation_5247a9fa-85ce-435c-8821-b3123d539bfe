package com.mira.user.controller.vo.cycle;

import com.mira.api.bluetooth.dto.cycle.TestDataDTO;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.enums.UserMenopauseStageEnum;
import com.mira.user.controller.vo.chart.PregnantRiskVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("周期分析")
public class CycleAnalysisVO extends Ultra4Analysis {
    /**
     * @see UserGoalEnum
     */
    @ApiModelProperty("用户目标")
    private Integer userMode;

    @ApiModelProperty("是否跟踪更年期，null无标识;0 不跟踪;1跟踪")
    private Integer trackingMenopause;

    /**
     * @see UserMenopauseStageEnum
     */
    @ApiModelProperty("menopause定义Stage的种类")
    private Integer menopauseStage;

    @ApiModelProperty("周期长度平均值")
    private Integer cycleLength;

    @ApiModelProperty("经期长度平均值")
    private Integer periodLength;

    @ApiModelProperty("黄体期长度平均值")
    private Integer lutealPhases;

    @ApiModelProperty("月经结束到排卵长度平均值")
    private Integer folicularPhases;

    @ApiModelProperty("排卵日Index平均值")
    private Integer ovulationEstimate;

    @ApiModelProperty("排卵日Index单位")
    private String ovulationEstimateUnit;

    /**
     * 需要针对menopause模式增加最近的FSH测试值
     */
    @ApiModelProperty("最后一条FSH测试数据")
    private TestDataDTO lastFsh;

    @ApiModelProperty("上一个测试的经期日期")
    private String lastMenstrualPeriodDate;

    @ApiModelProperty("经期延后多少天")
    private Integer periodDelay;

    @ApiModelProperty("周期数据List")
    private List<CycleData> cycleData;

    @Getter
    @Setter
    @ApiModel("周期数据")
    public static class CycleData {
        @ApiModelProperty("主键")
        private Integer cycleIndex;

        @ApiModelProperty("周期长度")
        private Integer lenCycle;

        @ApiModelProperty("周期开始日")
        private String datePeriodStart;

        @ApiModelProperty("经期结束日（经期不包含这天")
        private Integer indexPeriodEnd;

        @ApiModelProperty("易孕期开始日 (由 fertility soccer 》=6 计算)")
        private Integer indexFwStart;

        @ApiModelProperty("易孕期结束日 （易孕期不包含这一天）")
        private Integer indexFwEnd;

        @ApiModelProperty("预留，可为 null （实际测量的最高值时间）")
        private Integer indexLhSurgeDay;

        @ApiModelProperty("PDG rise")
        private List<Integer> indexPdgRises;

        @ApiModelProperty("sex日期index记录")
        private List<Integer> indexesOfSex;

        @ApiModelProperty("怀孕风险预测")
        private PregnantRiskVO pregnantRisk;
    }
}
