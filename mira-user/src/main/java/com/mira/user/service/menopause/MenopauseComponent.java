package com.mira.user.service.menopause;

import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.enums.OvulationTypeEnum;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.user.enums.UserMenopauseStageEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.NumberFormatUtil;
import com.mira.user.controller.vo.menopause.DataRangeVO;
import com.mira.user.controller.vo.menopause.MenopauseReportVO;
import com.mira.user.service.manager.CacheManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-10-25
 **/
@Component
public class MenopauseComponent {
    @Resource
    private CacheManager cacheManager;


    @NotNull
    public static List<MenopauseReportVO> buildMenopauseReports(Integer reportType, Float progressStatus, String defineStageDate,
                                                                String testPlanFirstEndDate, String testPlanSecondEndDate, boolean isRegular) {
        List<MenopauseReportVO> menopauseReports = new ArrayList<>();
        if (reportType != null) {
            //先看当前所处cycle
            if (progressStatus < 0.6f) {
                //一个预测report,取该周期的最后一个fsh测试日
                if (StringUtils.isNotBlank(testPlanFirstEndDate)) {
                    MenopauseReportVO menopauseReport = new MenopauseReportVO();
                    menopauseReport.setDate(testPlanFirstEndDate);
                    menopauseReport.setType(1);
                    menopauseReports.add(menopauseReport);
                }
            } else if (progressStatus > 0.6f && progressStatus < 1f) {
                //一个实际report + 一个预测report
                if (StringUtils.isNotBlank(defineStageDate)) {
                    MenopauseReportVO menopauseReport = new MenopauseReportVO();
                    menopauseReport.setDate(defineStageDate);
                    menopauseReport.setType(reportType);
                    menopauseReports.add(menopauseReport);
                }
                if (StringUtils.isNotBlank(testPlanSecondEndDate)) {
                    MenopauseReportVO menopauseReport = new MenopauseReportVO();
                    menopauseReport.setDate(testPlanSecondEndDate);
                    menopauseReport.setType(2);
                    menopauseReports.add(menopauseReport);
                }
            } else {
                // progressStatus == 1f || progressStatus == 0.6f
                //一个实际report
                if (StringUtils.isNotBlank(defineStageDate)) {
                    MenopauseReportVO menopauseReport = new MenopauseReportVO();
                    menopauseReport.setDate(defineStageDate);
                    menopauseReport.setType(reportType);
                    menopauseReports.add(menopauseReport);
                }
            }
        }
        return menopauseReports;
    }


    public static Integer getReportType(Integer defineStage) {
        Integer reportType = null;
        if (defineStage != null) {
            switch (UserMenopauseStageEnum.get(defineStage)) {
                case Stage1_late_reproductive_age:
                case Stage1_early_menopause_transition:
                case Stage1_late_menopause_transition:
                case Stage1_menopause:
                case Stage1_early_menopause:
                case Stage1_remature_menopause:
                case Stage1_post_menopause:
                case Stage1_undefined:
                    reportType = 1;
                    break;
                case Late_reproductive_age:
                case Early_menopause_transition:
                case Late_menopause_transition:
                case Menopause:
                case Early_menopause:
                case Remature_menopause:
                case Post_menopause:
                case Undefined:
                    reportType = 2;
                    break;
                default:
                    break;
            }
        }
        return reportType;
    }

    public static void setLhPickAndPdgRise(CycleDataDTO testCycle, String today, DataRangeVO dataRange) {
        Integer ovulationType = testCycle.getOvulation_type();
        if (OvulationTypeEnum.DETECTED.getCode() == ovulationType
                || OvulationTypeEnum.DETECTED_CONFIRMED.getCode() == ovulationType) {
            String dateLhSurge = testCycle.getDate_LH_surge();
            dataRange.setLhPeak(dateLhSurge);
        }
        if (OvulationTypeEnum.PREDICTED_CONFIRMED.getCode() == ovulationType
                || OvulationTypeEnum.DETECTED_CONFIRMED.getCode() == ovulationType) {
            List<String> datePdgRise = testCycle.getDate_PDG_rise();
            if (CollectionUtils.isNotEmpty(datePdgRise)) {
                dataRange.setPdgRise(datePdgRise.get(0));
            }
        }
    }

    public DataRangeVO getIntervalDataRange(List<HormoneDTO> hormoneDatas, String datePeriodStart, String dateCycleEnd) {
        DataRangeVO dataRange = new DataRangeVO();
        List<HormoneDTO.TestResult> testResults = hormoneDatas
                .stream()
                .filter(hormoneDTO -> LocalDateUtil.isBetweenDateAndEqualLeft(hormoneDTO.getTest_time().substring(0, 10),
                        datePeriodStart,
                        dateCycleEnd))
                .filter(hormoneDTO -> hormoneDTO.getFlag() == 1)
                .map(hormoneDTO -> hormoneDTO.getTest_results())
                .collect(Collectors.toList());
        List<Float> lhDataList = new ArrayList<>();
        List<Float> e3gDataList = new ArrayList<>();
        List<Float> pdgDataList = new ArrayList<>();
        List<Float> fshDataList = new ArrayList<>();
        testResults.stream()
                   .forEach(testResult -> {
                       if (WandTypeEnum.LH.getInteger().equals(testResult.getWand_type())) {
                           lhDataList.add(testResult.getValue1());
                       } else if (WandTypeEnum.E3G_LH.getInteger().equals(testResult.getWand_type())) {
                           lhDataList.add(testResult.getValue2());
                           e3gDataList.add(testResult.getValue1());
                       } else if (WandTypeEnum.LH_E3G_PDG.getInteger().equals(testResult.getWand_type())) {
                           lhDataList.add(testResult.getValue1());
                           pdgDataList.add(testResult.getValue2());
                           e3gDataList.add(testResult.getValue3());
                       } else if (WandTypeEnum.FSH.getInteger().equals(testResult.getWand_type())) {
                           fshDataList.add(testResult.getValue1());
                       }
                   });
        List<String> lhDatas = new ArrayList<>();
        List<String> e3gDatas = new ArrayList<>();
        List<String> pdgDatas = new ArrayList<>();
        List<String> fshDatas = new ArrayList<>();
        if (!lhDataList.isEmpty()) {
            if (lhDataList.size() == 1) {
                lhDatas.add(NumberFormatUtil.format(lhDataList.get(0)).toString());
            } else {
                lhDatas.add(NumberFormatUtil.format(Collections.min(lhDataList)).toString());
                lhDatas.add(NumberFormatUtil.format(Collections.max(lhDataList)).toString());
            }
        }
        if (!e3gDataList.isEmpty()) {
            if (e3gDataList.size() == 1) {
                e3gDatas.add(e3gDataList.get(0).toString());
            } else {
                e3gDatas.add(NumberFormatUtil.format(Collections.min(e3gDataList)).toString());
                e3gDatas.add(NumberFormatUtil.format(Collections.max(e3gDataList)).toString());
            }
        }
        if (!pdgDataList.isEmpty()) {
            if (pdgDataList.size() == 1) {
                pdgDatas.add(pdgDataList.get(0).toString());
            } else {
                pdgDatas.add(NumberFormatUtil.format(Collections.min(pdgDataList)).toString());
                pdgDatas.add(NumberFormatUtil.format(Collections.max(pdgDataList)).toString());
            }
        }
        if (!fshDataList.isEmpty()) {
            if (fshDataList.size() == 1) {
                fshDatas.add(fshDataList.get(0).toString());
            } else {
                fshDatas.add(NumberFormatUtil.format(Collections.min(fshDataList)).toString());
                fshDatas.add(NumberFormatUtil.format(Collections.max(fshDataList)).toString());
            }
        }
        dataRange.setLhDatas(lhDatas);
        dataRange.setE3gDatas(e3gDatas);
        dataRange.setPdgDatas(pdgDatas);
        dataRange.setFshDatas(fshDatas);
        return dataRange;
    }

    //对于规则周期和非规则周期，首先都要找到menopause下的第一个cycle和第二个cycle
    public List<CycleDataDTO> getFirstCycleAndSecondCycle(List<CycleDataDTO> cycleDataDTOS,
                                                          CycleDataDTO currentCycleData, String today,
                                                          Float progressStatus, String defineStageDate, boolean isRegular) {
        CycleDataDTO firstTestCycle = null;
        CycleDataDTO secondTestCycle = null;

        //对于规则周期
        if (progressStatus == 0.2f) {
            int cd = LocalDateUtil.minusToDay(today, currentCycleData.getDate_period_start()) + 1;
            List<String> product16 = currentCycleData.getTesting_day_list().getProduct16();
            //如果当天是 CD1/2/3且当前周期存在 FSH测试日，当前周期是第一个测试cycle,否则预测周期是第一个测试cycle
            if (isRegular) {
                if ((cd == 1 || cd == 2 || cd == 3) && !product16.isEmpty()) {
                    firstTestCycle = currentCycleData;
                    if (!firstTestCycle.getCycle_index().equals(cycleDataDTOS.size() - 1)) {
                        secondTestCycle = cycleDataDTOS.get(firstTestCycle.getCycle_index() + 1);
                    }
                } else {
                    //预测周期是第一个测试cycle，这个时候没有测试数据 //没有secondCycle
                    if (CycleStatusEnum.FORECAST_CYCLE.getStatus() != currentCycleData.getCycle_status()) {
                        firstTestCycle = cycleDataDTOS.get(currentCycleData.getCycle_index() + 1);
                    } else {
                        firstTestCycle = currentCycleData;
                    }
                    secondTestCycle = null;
                }
            } else {
                firstTestCycle = currentCycleData;
            }


        } else if (progressStatus > 0.2f && progressStatus <= 0.6f) {
            //当前周期是第一个测试cycle
            firstTestCycle = currentCycleData;
            //非规则周期下，当前周期可能为预测周期（用户一年没有经期会出现这种情况）
            if (!firstTestCycle.getCycle_index().equals(cycleDataDTOS.size() - 1)) {
                secondTestCycle = cycleDataDTOS.get(firstTestCycle.getCycle_index() + 1);
            }
        } else if (progressStatus > 0.6f && progressStatus <= 1f) {
            //第一个测试cycle已经过了,可以有两个cycle的数据
            // 当前周期处于第二个 cycle
            secondTestCycle = currentCycleData;
            firstTestCycle = cycleDataDTOS.get(secondTestCycle.getCycle_index() - 1);
        } else {
            // 当前周期处于第二个 cycle结束了fsh测试日 或者，当前周期已经过了第二个cycle
            // defineStageDate所在的周期是第二个cycle
            secondTestCycle = CycleDataUtil.getCurrentCycleData(defineStageDate, cycleDataDTOS);
            firstTestCycle = cycleDataDTOS.get(secondTestCycle.getCycle_index() - 1);
        }

        List<CycleDataDTO> firstCycleAndSecondCycle = new ArrayList<>();
        if (firstTestCycle != null) {
            firstCycleAndSecondCycle.add(firstTestCycle);
        }
        if (secondTestCycle != null) {
            firstCycleAndSecondCycle.add(secondTestCycle);
        }
        return firstCycleAndSecondCycle;
    }


}
