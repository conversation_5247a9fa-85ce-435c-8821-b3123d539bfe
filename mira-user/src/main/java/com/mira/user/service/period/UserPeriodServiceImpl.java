package com.mira.user.service.period;

import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.enums.*;
import com.mira.api.bluetooth.provider.IAlgorithmProvider;
import com.mira.api.bluetooth.util.CycleDataUtil;
import com.mira.api.bluetooth.util.PeriodUtil;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.enums.NoPeriodGoalEnum;
import com.mira.api.user.util.UserGoalUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.*;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.async.KlaviyoProducer;
import com.mira.user.async.PatientProducer;
import com.mira.user.controller.vo.period.CheckOvuVO;
import com.mira.user.controller.vo.period.UserPeriodVO;
import com.mira.user.dal.dao.*;
import com.mira.user.dal.entity.*;
import com.mira.user.dto.calendar.EditPeriodDTO;
import com.mira.user.dto.info.UserCycleLengthDTO;
import com.mira.user.dto.info.UserPeriodLengthDTO;
import com.mira.user.enums.calendar.EditPeriodReturnEnum;
import com.mira.user.exception.UserException;
import com.mira.user.properties.UserProperties;
import com.mira.user.service.manager.AlgorithmCallManager;
import com.mira.user.service.manager.CacheManager;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import com.mira.user.service.util.CallEditPeriodUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 用户经期接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service("userPeriodService")
public class UserPeriodServiceImpl implements IUserPeriodService {
    private final AppUserPeriodDAO appUserPeriodDAO;
    private final AppOvulationManualDAO appOvulationManualDAO;
    private final AppOvulationManualNoteDAO appOvulationManualNoteDAO;
    private final UserPeriodEditNoteDAO userPeriodEditNoteDAO;
    private final UserPeriodEditLogDAO userPeriodEditLogDAO;
    private final CacheManager cacheManager;
    private final AlgorithmCallManager algorithmCallManager;
    private final IAlgorithmProvider algorithmProvider;
    private final ISsoProvider ssoProvider;
    private final PatientProducer patientProducer;
    private final KlaviyoProducer klaviyoProducer;

    private final UserProperties userProperties;
    private final AppUserInfoDAO appUserInfoDAO;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editPeriodLength(UserPeriodLengthDTO userPeriodLengthDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);

        // no period check
        if (Objects.equals(1, userPeriodLengthDTO.getNoPeriodFlag())) {
            // already in no period mode, no action
            if (NoPeriodGoalEnum.get(loginUserInfoDTO.getNoPeriodFlag()) != null) {
                return;
            }
            // enter no period mode
            AlgorithmResultDTO cacheAlgorithmResult = cacheManager.getCacheAlgorithmResult(userId);
            List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(cacheAlgorithmResult.getCycleData(), CycleDataDTO.class);
            boolean inPeriod = CycleDataUtil.inPeriod(today, CycleDataUtil.getCurrentCycleData(today, cycleDataDTOS));
            if (inPeriod) {
                throw new UserException(BizCodeEnum.NO_PERIOD_NOT_ENTER);
            }
            algorithmCallManager.editDBPeriod(loginUserInfoDTO, AlgorithmRequestTypeEnum.EDIT_NO_PERIOD);
        }
        if (Objects.equals(0, userPeriodLengthDTO.getNoPeriodFlag())) {
            AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(loginUserInfoDTO.getUserId());
            appUserInfoEntity.setNoPeriod(-1);
            appUserInfoDAO.updateById(appUserInfoEntity);
            // cache
            cacheManager.deleteUserDetailCache(userId);
        }

        // entity check
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (ObjectUtils.isEmpty(appUserPeriodEntity)) {
            log.info("user:{} has not exist app_user_period", userId);
            return;
        }
        // params
        Integer avgLenPeriod = userPeriodLengthDTO.getAvgLenPeriod();
        Integer periodFlag = userPeriodLengthDTO.getPeriodFlag();
        // update
        appUserPeriodEntity.setAvgLenPeriod(avgLenPeriod);
        appUserPeriodEntity.setPeriodFlag(periodFlag);
        appUserPeriodEntity.setModifier(userId);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUserPeriodEntity);
        appUserPeriodDAO.updateById(appUserPeriodEntity);
        // build period info
        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity);
        userPeriodParamDTO.setPeriodFlag(periodFlag);
        // call algorithm
        PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(timeZone, loginUserInfoDTO.getEmail(),
                appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
        algorithmCallManager.editPeriod(prepareEditPeriodDTO, AlgorithmRequestTypeEnum.EDIT_PERIOD_LENGTH);
        // clinic
        patientProducer.periodChange(userId, AlgorithmRequestTypeEnum.EDIT_PERIOD_LENGTH);
        // cache
        cacheManager.deleteUserDetailCache(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editCycleLength(UserCycleLengthDTO userCycleLengthDTO) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // entity check
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (ObjectUtils.isEmpty(appUserPeriodEntity)) {
            log.info("user:{} has not exist UserPeriod", userId);
            return;
        }
        // update app user info
        AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(userId);
        appUserInfoEntity.setIrregularCycle(userCycleLengthDTO.getIrregularCycle());
        appUserInfoDAO.updateById(appUserInfoEntity);
        // params
        Integer avgLenCycle = userCycleLengthDTO.getAvgLenCycle();
        Integer cycleFlag = userCycleLengthDTO.getCycleFlag();
        // sync kalviyo
        klaviyoProducer.editCycleLength(userId, avgLenCycle);
        // cycle flag
        if (cycleFlag == null) {
            cycleFlag = 0;
        }
        // whether to update
        if (avgLenCycle == null
                || (avgLenCycle.equals(appUserPeriodEntity.getAvgLenCycle())
                && cycleFlag.equals(appUserPeriodEntity.getCycleFlag()))) {
            log.info("userId:{} param avgLenCycle not changed", userId);
            // cache
            cacheManager.deleteUserDetailCache(userId);
            return;
        }
        // update app user period
        appUserPeriodEntity.setAvgLenCycle(avgLenCycle);
        appUserPeriodEntity.setCycleFlag(cycleFlag);
        appUserPeriodEntity.setModifier(userId);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUserPeriodEntity);
        appUserPeriodDAO.updateById(appUserPeriodEntity);
        // build period info
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity);
        userPeriodParamDTO.setCycleFlag(cycleFlag);
        // call algorithm
        PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(timeZone, loginUserInfoDTO.getEmail(),
                appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
        algorithmCallManager.editPeriod(prepareEditPeriodDTO, AlgorithmRequestTypeEnum.EDIT_CYCLE_LENGTH);
        // clinic
        patientProducer.periodChange(userId, AlgorithmRequestTypeEnum.EDIT_CYCLE_LENGTH);
        // cache
        cacheManager.deleteUserDetailCache(userId);
    }

    @Override
    public UserPeriodVO info() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();

        // entity check
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (ObjectUtils.isEmpty(appUserPeriodEntity)) {
            throw new UserException("user period not exist");
        }

        UserPeriodVO userPeriodVO = new UserPeriodVO();
        userPeriodVO.setAvgLenPeriod(appUserPeriodEntity.getAvgLenPeriod());
        String periods = appUserPeriodEntity.getPeriods();
        List<String> periodsStr = new ArrayList<>();
        if (StringUtils.isNotBlank(periods)) {
            List<Long> dbPeriodList = StringListUtil.strToLongList(appUserPeriodEntity.getPeriods(), ",");
            // convert to user local time
            periodsStr = PeriodUtil.periodsLong2String(dbPeriodList, appUserPeriodEntity.getTimeZone());
        }
        userPeriodVO.setPeriods(periodsStr);

        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        userPeriodVO.setUserMode(UserGoalUtil.getUserGoalEnum(loginUserInfoDTO).getValue());

        return userPeriodVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer updatePeriod(List<String> periods) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        String email = loginInfo.getUsername();

        // entity check
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            throw new UserException("user period not exist");
        }
        String dbPeriods = appUserPeriodEntity.getPeriods();
        // whether to update
        if (!toUdpate(timeZone, email, periods, appUserPeriodEntity, true)) {
            return null;
        }
        // common update logic
        List<UserPeriodDataDTO> userPeriodDataDTOS = commonPeriodUpdateLogic(timeZone, periods,
                appUserPeriodEntity, true);
        // build period info
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity, userPeriodDataDTOS);
        // call algorithm
        Integer thresholdMode = callEditPeriod(userId, timeZone, email, appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
        // check edit period operation count by user
        Long userEditPeriodCount = userPeriodEditLogDAO.getCountByUserId(userId);
        if (whetherCheckFirstLogPeriod(appUserPeriodEntity, timeZone)
                && EditPeriodReturnEnum.FIST_EDIT_IN_APP.getEditCount() == userEditPeriodCount) {
            // edit period log
            editPeriodLog(userId, timeZone, checkPeriodChanges(timeZone, periods, dbPeriods));
            return EditPeriodReturnEnum.FIST_EDIT_IN_APP.getReturnValue();
        }
        // extra operation
        updatePeriodsExtraOperation(userId, userPeriodParamDTO.getUserMode(), timeZone,
                periods, appUserPeriodEntity.getPeriods(), userPeriodDataDTOS);

        return thresholdMode;
    }

    @Override
    public Integer updatePeriodV5(EditPeriodDTO editPeriodDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        String email = loginInfo.getUsername();

        // entity check
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            throw new UserException("user period not exist");
        }
        String dbPeriods = appUserPeriodEntity.getPeriods();
        // periods and predicted periods
        List<String> predictedPeriods = editPeriodDTO.getPredictedPeriods();
        List<String> periods = editPeriodDTO.getPeriods();
        // whether to update
        if (!toUdpate(timeZone, email, periods, appUserPeriodEntity, false)) {
            return null;
        }
        // common update logic
        List<UserPeriodDataDTO> userPeriodDataDTOS = commonPeriodUpdateLogic(timeZone, periods,
                appUserPeriodEntity, true);
        // handle predicted periods
        handlePredictedPeriod(userId, userPeriodDataDTOS, predictedPeriods);
        // build period info
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity, userPeriodDataDTOS);
        // handle manual ovulation
        handleManualOvulation(userId, timeZone, userPeriodDataDTOS);
        // call algorithm
        Integer thresholdMode = callEditPeriod(userId, timeZone, email, appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
        // check edit period operation count by user
        Long userEditPeriodCount = userPeriodEditLogDAO.getCountByUserId(userId);
        if (whetherCheckFirstLogPeriod(appUserPeriodEntity, timeZone)
                && EditPeriodReturnEnum.FIST_EDIT_IN_APP.getEditCount() == userEditPeriodCount) {
            // edit period log
            editPeriodLog(userId, timeZone, checkPeriodChanges(timeZone, periods, dbPeriods));
            // cache
            cacheManager.deleteUserDetailCache(userId);
            return EditPeriodReturnEnum.FIST_EDIT_IN_APP.getReturnValue();
        }
        // extra operation
        updatePeriodsExtraOperation(userId, userPeriodParamDTO.getUserMode(), timeZone,
                periods, dbPeriods, userPeriodDataDTOS);

        return thresholdMode;
    }

    private boolean toUdpate(String timeZone, String email, List<String> periods,
                             AppUserPeriodEntity appUserPeriodEntity,
                             boolean checkEquals) {
        // user id
        Long userId = appUserPeriodEntity.getId();
        // sort
        List<Long> sortedPeriodList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(periods)) {
            List<Long> periodsLong = PeriodUtil.periodsString2Long(periods, timeZone);
            sortedPeriodList = periodsLong.stream().sorted(Long::compareTo).collect(Collectors.toList());
        }
        // whether to update
        String dbPeriods = appUserPeriodEntity.getPeriods();
        String periodsStr = StringListUtil.listToString(sortedPeriodList, ",");
        if (StringUtils.isBlank(periodsStr) && StringUtils.isBlank(dbPeriods)) {
            log.info("user:{},email:{} periods empty", userId, email);
            return false;
        }
        if (checkEquals && StringUtils.isNotBlank(periodsStr) && periodsStr.equals(dbPeriods)) {
            log.info("user:{},email:{} periods not changed", userId, email);
            return false;
        }
        return true;
    }

    private void handleManualOvulation(Long userId, String timeZone,
                                       List<UserPeriodDataDTO> userPeriodDataDTOS) {
        AppOvulationManualEntity ovulationManualEntity = appOvulationManualDAO.getByUserId(userId);
        if (ovulationManualEntity == null) {
            return;
        }
        String ovuData = ovulationManualEntity.getOvuData();
        if (StringUtils.isBlank(ovuData)) {
            return;
        }
        // algorithm data
        AlgorithmResultDTO cacheAlgorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(cacheAlgorithmResult.getCycleData(), CycleDataDTO.class);
        if (CollectionUtils.isEmpty(cycleDataDTOS)) {
            return;
        }
        // manual ovu list
        List<String> manualOvuDataList = JsonUtil.toArray(ovuData, String.class);
        // current period mapping manual ovu
        Map<String, String> currentPeriodMappingManualOvu = cycleDataDTOS.stream().filter(cycleDataDTO ->
                        StringUtils.isNotBlank(cycleDataDTO.getDate_ovulation())
                                && OvulationTypeEnum.CUSTOM.getCode().equals(cycleDataDTO.getOvulation_type()))
                .collect(Collectors.toMap(CycleDataDTO::getDate_period_start, CycleDataDTO::getDate_ovulation));
        // new period list
        List<String> newPeriodList = userPeriodDataDTOS.stream().map(UserPeriodDataDTO::getDate_period_start)
                .collect(Collectors.toList());
        // if the period has been deleted, all manual ovu in this cycle will be deleted
        List<String> waitDeleteManualOvu = new ArrayList<>();
        Set<String> currentPeriodSet = currentPeriodMappingManualOvu.keySet();
        for (String currentPeriod : currentPeriodSet) {
            if (!newPeriodList.contains(currentPeriod)) {
                String waitManualOvu = currentPeriodMappingManualOvu.get(currentPeriod);
                waitDeleteManualOvu.add(waitManualOvu);
            }
        }

        // update ovulation manual
        if (CollectionUtils.isNotEmpty(waitDeleteManualOvu)) {
            manualOvuDataList.removeAll(waitDeleteManualOvu);
            ovulationManualEntity.setOvuData(JsonUtil.toJson(manualOvuDataList));
            UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, ovulationManualEntity);
            appOvulationManualDAO.updateById(ovulationManualEntity);
            // add ovulation manual note
            AppOvulationManualNoteEntity ovulationManualNoteEntity = new AppOvulationManualNoteEntity();
            ovulationManualNoteEntity.setUserId(userId);
            ovulationManualNoteEntity.setRemoveOvu(JsonUtil.toJson(waitDeleteManualOvu));
            ovulationManualNoteEntity.setNote("编辑经期时去掉多余的手动排卵日");
            ovulationManualNoteEntity.setCreateTime(ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN));
            ovulationManualNoteEntity.setTimeZone(timeZone);
            appOvulationManualNoteDAO.save(ovulationManualNoteEntity);
        }
    }

    private List<UserPeriodDataDTO> commonPeriodUpdateLogic(String timeZone, List<String> periods,
                                                            AppUserPeriodEntity appUserPeriodEntity,
                                                            boolean updateEntity) {
        // user id
        Long userId = appUserPeriodEntity.getId();
        // sort
        List<Long> sortedPeriodList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(periods)) {
            List<Long> periodsLong = PeriodUtil.periodsString2Long(periods, timeZone);
            sortedPeriodList = periodsLong.stream().sorted(Long::compareTo).collect(Collectors.toList());
        }
        String periodsStr = StringListUtil.listToString(sortedPeriodList, ",");

        // build user period data
        List<UserPeriodDataDTO> userPeriodDataDTOS = CallEditPeriodUtil.buildUserPeriodDataDTOS(timeZone, sortedPeriodList,
                appUserPeriodEntity.getCutPoints(), appUserPeriodEntity.getAvgLenPeriod());

        // update
        String periodListJson = JsonUtil.toJson(userPeriodDataDTOS);
        appUserPeriodEntity.setPeriods(StringUtils.isBlank(periodsStr) ? "" : periodsStr);
        appUserPeriodEntity.setPeriodData(periodListJson);
        appUserPeriodEntity.setModifier(userId);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUserPeriodEntity);
        if (updateEntity) {
            appUserPeriodDAO.updateById(appUserPeriodEntity);
        }

        return userPeriodDataDTOS;
    }

    private Integer callEditPeriod(Long userId, String timeZone, String email, String newDbPeriods,
                                   UserPeriodParamDTO userPeriodParamDTO) {
        // app user info
        AppUserInfoEntity appUserInfoEntity = appUserInfoDAO.getByUserId(userId);

        // check no period mode
        if (NoPeriodGoalEnum.get(appUserInfoEntity.getNoPeriod()) != null) {
            // 退出无经期模式
            appUserInfoEntity.setNoPeriod(-1);
            appUserInfoDAO.updateById(appUserInfoEntity);
        }

        // call algorithm
        PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(timeZone,
                email, newDbPeriods, userPeriodParamDTO);

        return algorithmCallManager.editPeriod(prepareEditPeriodDTO, AlgorithmRequestTypeEnum.EDIT_PERIOD);
    }

    private void updatePeriodsExtraOperation(Long userId, Integer userMode, String timeZone,
                                             List<String> periods, String dbPeriods,
                                             List<UserPeriodDataDTO> userPeriodDataDTOS) {
        // edit period log
        editPeriodLog(userId, timeZone, checkPeriodChanges(timeZone, periods, dbPeriods));
        // clinic
        patientProducer.periodChange(userId, AlgorithmRequestTypeEnum.EDIT_PERIOD);
        // klavyio
        klaviyoProducer.periodEdit(userId, userMode, userPeriodDataDTOS);
    }

    private boolean whetherCheckFirstLogPeriod(AppUserPeriodEntity userPeriodEntity, String timeZone) {
        String userRegisterTime = ZoneDateUtil.format(timeZone, userPeriodEntity.getCreateTime(), DatePatternConst.DATE_PATTERN);
        String period1stlogRelease = userProperties.getPeriod1stlogRelease();
        return LocalDateUtil.after(userRegisterTime, period1stlogRelease, DatePatternConst.DATE_PATTERN);
    }

    private Map<String, Set<Long>> checkPeriodChanges(String timeZone, List<String> periods, String dbPeriods) {
        Map<String, Set<Long>> result = new HashMap<>();

        List<Long> updatePeriodsList = PeriodUtil.periodsString2Long(periods, timeZone);
        List<Long> existPeriodsList = StringListUtil.strToLongList(dbPeriods, ",");

        Set<Long> updatePeriodsSet = new HashSet<>(updatePeriodsList);
        Set<Long> existPeriodsSet = new HashSet<>(existPeriodsList);

        if (existPeriodsSet.equals(updatePeriodsSet)) {
            log.info("the same elements (edit period)");
            // same elements
            return result;
        }

        // find elements to remove (in exist but not in update)
        Set<Long> toRemove = new HashSet<>(existPeriodsSet);
        toRemove.removeAll(updatePeriodsSet);
        result.put("toRemove", toRemove);

        // find elements to add (in update but not in exist)
        Set<Long> toAdd = new HashSet<>(updatePeriodsSet);
        toAdd.removeAll(existPeriodsSet);
        result.put("toAdd", toAdd);

        return result;
    }

    private void editPeriodLog(Long userId, String timeZone, Map<String, Set<Long>> periodChanges) {
        CompletableFuture.runAsync(() -> {
            // log entity
            UserPeriodEditLogEntity userPeriodEditLogEntity = new UserPeriodEditLogEntity();

            if (periodChanges.isEmpty()) {
                return;
            }

            Set<Long> toRemove = periodChanges.get("toRemove");
            if (CollectionUtils.isNotEmpty(toRemove)) {
                userPeriodEditLogEntity.setRemovePeriod(JsonUtil.toJson(toRemove));
            }

            Set<Long> toAdd = periodChanges.get("toAdd");
            if (CollectionUtils.isNotEmpty(toAdd)) {
                userPeriodEditLogEntity.setAddPeriod(JsonUtil.toJson(toAdd));
            }

            userPeriodEditLogEntity.setUserId(userId);
            userPeriodEditLogEntity.setTimeZone(timeZone);
            userPeriodEditLogEntity.setCreateTime(
                    ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN));
            userPeriodEditLogDAO.save(userPeriodEditLogEntity);
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("save edit period log error, user:{}", userId, ex);
            return null;
        });
    }

    private void handlePredictedPeriod(Long userId, List<UserPeriodDataDTO> userPeriodDataDTOS, List<String> predictedPeriods) {
        // get alogrithm record
        AlgorithmResultDTO algorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResult.getCycleData(), CycleDataDTO.class);
        if (CollectionUtils.isNotEmpty(cycleDataDTOS) && cycleDataDTOS.size() < 3) {
            return;
        }
        Map<String, Integer> periodStartAndIndexMap = cycleDataDTOS.stream()
                .filter(cycleDataDTO -> StringUtils.isNotBlank(cycleDataDTO.getDate_period_end()))
                .collect(Collectors.toMap(CycleDataDTO::getDate_period_start, CycleDataDTO::getCycle_index));
        // get current predicted periods
        List<String> currentPredictedPeriodList = CycleDataUtil.getPeriodList(cycleDataDTOS, CycleStatusEnum.FORECAST_CYCLE);
        // which days have been removed
        currentPredictedPeriodList.removeAll(predictedPeriods);
        // user period edit note
        UserPeriodEditNoteEntity userPeriodEditNoteEntity = new UserPeriodEditNoteEntity();

        if (CollectionUtils.isNotEmpty(currentPredictedPeriodList)) {
            int size = userPeriodDataDTOS.size();
            String predicatedStart = currentPredictedPeriodList.get(0);
            for (int index = 1; index < size; index++) {
                UserPeriodDataDTO userPeriodDataDTO = userPeriodDataDTOS.get(index);
                String datePeriodStart = userPeriodDataDTO.getDate_period_start();
                int days = LocalDateUtil.minusToDay(datePeriodStart, predicatedStart);
                if (days > 0) {
                    UserPeriodDataDTO previousPeriodData = userPeriodDataDTOS.get(index - 1);
                    previousPeriodData.setFlag(LastCycleFlagEnum.ONE.getFlag());
                    previousPeriodData.setLen_cycle(
                            LocalDateUtil.minusToDay(userPeriodDataDTO.getDate_period_start(),
                                    previousPeriodData.getDate_period_start()));
                    setUserPeriodEditNoteEntity(userPeriodEditNoteEntity, previousPeriodData, periodStartAndIndexMap);
                    break;
                }
            }
            // save user period edit note
            if (userPeriodEditNoteEntity.getPreviousIndex() != null) {
                saveUserPeriodEditNoteEntity(userId, userPeriodEditNoteEntity, algorithmResult, currentPredictedPeriodList);
            }
        }
    }

    private void setUserPeriodEditNoteEntity(UserPeriodEditNoteEntity userPeriodEditNoteEntity,
                                             UserPeriodDataDTO previousPeriodData,
                                             Map<String, Integer> periodStartAndIndexMap) {
        String datePeriodStart = previousPeriodData.getDate_period_start();
        Integer cycleIndex = periodStartAndIndexMap.get(datePeriodStart);
        if (cycleIndex != null) {
            userPeriodEditNoteEntity.setPreviousIndex(cycleIndex);
            userPeriodEditNoteEntity.setPreviousStart(datePeriodStart);
            userPeriodEditNoteEntity.setLenCycle(previousPeriodData.getLen_cycle());
        }
    }

    private void saveUserPeriodEditNoteEntity(Long userId, UserPeriodEditNoteEntity userPeriodEditNoteEntity,
                                              AlgorithmResultDTO algorithmResult,
                                              List<String> delPredictedPeriodList) {
        CompletableFuture.runAsync(() -> {
            UserPeriodEditNoteEntity existUserPeriodEditNote = userPeriodEditNoteDAO.getByUserIdAndStart(
                    userId, userPeriodEditNoteEntity.getPreviousStart());
            if (existUserPeriodEditNote != null) {
                existUserPeriodEditNote.setDelPredicted(JsonUtil.toJson(delPredictedPeriodList));
                existUserPeriodEditNote.setLenCycle(userPeriodEditNoteEntity.getLenCycle());
                existUserPeriodEditNote.setUpdateTime(ZoneDateUtil.format(algorithmResult.getTimeZone(),
                        System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN));
                userPeriodEditNoteDAO.updateById(existUserPeriodEditNote);
                return;
            }

            userPeriodEditNoteEntity.setDelPredicted(JsonUtil.toJson(delPredictedPeriodList));
            userPeriodEditNoteEntity.setUserId(userId);
            userPeriodEditNoteEntity.setTimeZone(algorithmResult.getTimeZone());
            userPeriodEditNoteEntity.setCreateTime(ZoneDateUtil.format(userPeriodEditNoteEntity.getTimeZone(),
                    System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN));
            userPeriodEditNoteEntity.setUpdateTime(userPeriodEditNoteEntity.getCreateTime());
            userPeriodEditNoteDAO.save(userPeriodEditNoteEntity);
        }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
            log.error("user:{} user_period_edit_note save error", userId, ex);
            return null;
        });
    }

    @Override
    public CheckOvuVO checkWhetherOvuChange(EditPeriodDTO editPeriodDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        String email = loginInfo.getUsername();

        CheckOvuVO checkOvuVO = new CheckOvuVO();
        checkOvuVO.setType(1);

        // entity check
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            throw new UserException("user period not exist");
        }
        // periods and predicted periods
        List<String> predictedPeriods = editPeriodDTO.getPredictedPeriods();
        List<String> periods = editPeriodDTO.getPeriods();
        // whether to update
        if (!toUdpate(timeZone, email, periods, appUserPeriodEntity, false)) {
            return null;
        }
        // common update logic
        List<UserPeriodDataDTO> userPeriodDataDTOS = commonPeriodUpdateLogic(timeZone, periods,
                appUserPeriodEntity, false);
        if (CollectionUtils.isEmpty(userPeriodDataDTOS)) {
            return checkOvuVO;
        }
        // handle predicted periods
        handlePredictedPeriod(userId, userPeriodDataDTOS, predictedPeriods);
        // build period info
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity, userPeriodDataDTOS);
        // call algorithm
        PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(timeZone,
                email, appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
        AlgorithmResultDTO afterAlgorithmResult = algorithmCallManager.editPeriodNotUpdateDB(prepareEditPeriodDTO, AlgorithmRequestTypeEnum.EDIT_PERIOD);
        // calc value
        AlgorithmResultDTO previousAlgorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        List<String> previousAllConfirmOvuDayList = CycleDataUtil.getAllConfirmOvuDayList(JsonUtil.toArray(previousAlgorithmResult.getCycleData(), CycleDataDTO.class));
        List<String> afterAllConfirmOvuDayList = CycleDataUtil.getAllConfirmOvuDayList(JsonUtil.toArray(afterAlgorithmResult.getCycleData(), CycleDataDTO.class));
        afterAllConfirmOvuDayList.removeAll(previousAllConfirmOvuDayList);
        checkOvuVO.setValue(afterAllConfirmOvuDayList.isEmpty() ? 0 : 1);

        return checkOvuVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sysTestUpdatePeriod() {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        // entity check
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            throw new UserException("user period not exist");
        }

        // call algorithm
        algorithmCallManager.editDBPeriod(ssoProvider.getUserLoginInfo(userId).getData(), AlgorithmRequestTypeEnum.EDIT_PERIOD);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeThresholdMode(Integer thresholdMode) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();

        if (thresholdMode == ThresholdModeEnum.NORMAL.getCode()) {
            algorithmProvider.updateAlgorithmThreshold(userId, thresholdMode);
            cacheManager.delCacheAlgorithmResult(userId);
            return;
        }

        if (thresholdMode == ThresholdModeEnum.LOW_LH.getCode()) {
            AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
            String periods = appUserPeriodEntity.getPeriods();
            if (StringUtils.isBlank(periods)) {
                log.info("user:{}, email:{} periods empty", userId, loginInfo.getUsername());
                return;
            }

            // call algorithm
            algorithmCallManager.editDBPeriod(ssoProvider.getUserLoginInfo(userId).getData(), AlgorithmRequestTypeEnum.CHANGE_THRESHOLD_MODE);
        }
    }

    @Override
    public Integer checkRecentPeriodDateChange(List<String> periods) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String timeZone = loginInfo.getTimeZone();
        String email = loginInfo.getUsername();
        int recentPeriodDateChange = 0;

        // entity check
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            throw new UserException("user period not exist");
        }

        // sort
        List<Long> sortedPeriodList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(periods)) {
            List<Long> periodsLong = PeriodUtil.periodsString2Long(periods, timeZone);
            sortedPeriodList = periodsLong.stream().sorted(Long::compareTo).collect(Collectors.toList());
        }

        // whether to update
        String dbPeriods = appUserPeriodEntity.getPeriods();
        String periodsStr = StringListUtil.listToString(sortedPeriodList, ",");
        if (StringUtils.isBlank(periodsStr) && StringUtils.isBlank(dbPeriods)) {
            log.info("user:{}, email:{} periods empty", userId, email);
            return recentPeriodDateChange;
        }
        if (StringUtils.isNotBlank(periodsStr) && periodsStr.equals(dbPeriods)) {
            log.info("user:{}, email:{} periods not changed", userId, email);
            return recentPeriodDateChange;
        }

        // build user period data
        List<UserPeriodDataDTO> userPeriodDataDTOS = CallEditPeriodUtil.buildUserPeriodDataDTOS(timeZone, sortedPeriodList,
                appUserPeriodEntity.getCutPoints(), appUserPeriodEntity.getAvgLenPeriod());

        String periodData = appUserPeriodEntity.getPeriodData();
        if (StringUtils.isBlank(periodData)) {
            recentPeriodDateChange = 1;
            return recentPeriodDateChange;
        }

        int userPeriodDataSize = userPeriodDataDTOS.size();
        if (userPeriodDataSize == 0) {
            recentPeriodDateChange = 1;
            return recentPeriodDateChange;
        }

        List<UserPeriodDataDTO> dbUserPeriodDataDTOS = JsonUtil.toArray(periodData, UserPeriodDataDTO.class);
        String datePeriodStart = userPeriodDataDTOS.get(userPeriodDataSize - 1).getDate_period_start();
        int dbUserPeriodDataSize = dbUserPeriodDataDTOS.size();
        if (dbUserPeriodDataSize == 0) {
            recentPeriodDateChange = 1;
        } else {
            String dbDatePeriodStart = dbUserPeriodDataDTOS.get(dbUserPeriodDataSize - 1).getDate_period_start();
            if (!dbDatePeriodStart.equals(datePeriodStart)) {
                recentPeriodDateChange = 1;
            }
        }
        return recentPeriodDateChange;
    }
}
