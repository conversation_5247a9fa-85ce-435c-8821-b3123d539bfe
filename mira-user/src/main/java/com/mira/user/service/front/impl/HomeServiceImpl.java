package com.mira.user.service.front.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.cycle.PregnantRiskDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.dto.period.UserPeriodParamDTO;
import com.mira.api.bluetooth.dto.wand.*;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.provider.IBluetoothProvider;
import com.mira.api.bluetooth.util.TestRemindUtil;
import com.mira.api.clinic.dto.UserClinicDTO;
import com.mira.api.clinic.provider.IClinicProvider;
import com.mira.api.mongo.provider.ISurveyProvider;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.sso.provider.ISsoProvider;
import com.mira.api.user.dto.backend.HomeBannerDTO;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.diary.CustomLogConfigDTO;
import com.mira.api.user.dto.user.diary.UserDiaryMoodsDTO;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.api.user.util.UserGoalUtil;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.TempUnitEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.*;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.user.async.PatientProducer;
import com.mira.user.controller.vo.chart.PregnantRiskVO;
import com.mira.user.controller.vo.diary.CustomlogExistVO;
import com.mira.user.controller.vo.home.HomeBannerVO;
import com.mira.user.controller.vo.home.HomeDailyDataVO;
import com.mira.user.controller.vo.home.HomeDataVO;
import com.mira.user.dal.dao.*;
import com.mira.user.dal.entity.*;
import com.mira.user.dto.home.HomeShowWordDTO;
import com.mira.user.enums.home.BannerBindCheckEnum;
import com.mira.user.enums.home.BannerUserTypeEnum;
import com.mira.user.enums.home.HomeShowWordEnum;
import com.mira.user.exception.UserException;
import com.mira.user.service.front.IHomeService;
import com.mira.user.service.manager.*;
import com.mira.user.service.manager.model.PrepareEditPeriodDTO;
import com.mira.user.service.util.CallEditPeriodUtil;
import com.mira.user.service.util.CurrencyUtil;
import com.mira.user.service.util.CycleShowWordUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.RoundingMode;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * APP首页接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class HomeServiceImpl implements IHomeService {
    private final AppUserBindLogDAO appUserBindLogDAO;
    private final AppUserDiaryDAO appUserDiaryDAO;
    private final AppUserDiarySymptomsDAO appUserDiarySymptomsDAO;
    private final SysHomeBannerDAO sysHomeBannerDAO;
    private final AppUserDAO appUserDAO;
    private final AppUserInfoDAO appUserInfoDAO;
    private final AppUserPeriodDAO appUserPeriodDAO;
    private final AppUserDiaryMoodsDAO appUserDiaryMoodsDAO;
    private final UserCustomLogManager userCustomLogManager;
    private final UserDiaryLogManager userDiaryLogManager;
    private final AlgorithmCallManager algorithmCallManager;
    private final ExtendPeriodManager extendPeriodManager;
    private final CacheManager cacheManager;
    private final CurrencyUtil currencyUtil;
    private final IBluetoothProvider bluetoothProvider;
    private final PatientProducer patientProducer;
    private final ISsoProvider ssoProvider;
    private final IClinicProvider clinicProvider;
    private final ISurveyProvider surveyProvider;

    @Override
    public List<HomeBannerVO> list() {
        BaseLoginInfo loginInfo = ContextHolder.<BaseLoginInfo>getLoginInfo();
        Long userId = loginInfo.getId();
        String email = loginInfo.getUsername();

        AppUserEntity appUser = appUserDAO.getById(userId);
        if (Objects.isNull(appUser)) {
            throw new UserException("user does not exist or has been deleted");
        }
        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(userId);
        String currentCurrency = StringUtils.isEmpty(appUser.getCurrentCurrency())
                ? currencyUtil.getCurrentCurrency(appUser) : appUser.getCurrentCurrency();

        // 设备绑定记录标识
        boolean deviceBindLogFlag = true;
        long count = appUserBindLogDAO.countByUserId(userId);
        if (count == 0) {
            deviceBindLogFlag = false;
        }
        // userTypes 过滤
        Set<SysHomeBannerEntity> bannerSet = filterByUserGoal(appUserInfo.getGoalStatus());
        // 绑定仪器历史过滤
        if (deviceBindLogFlag) {
            bannerSet.removeIf(sysHomeBannerEntity ->
                    sysHomeBannerEntity.getBindCheck() == BannerBindCheckEnum.NEED_HISTORY_NO_BIND.getCode());
        } else {
            // 没有绑定过，需要删除为2的
            bannerSet.removeIf(sysHomeBannerEntity ->
                    sysHomeBannerEntity.getBindCheck() == BannerBindCheckEnum.NEED_HISTORY_BIND.getCode());
        }
        // 过滤未激活、未开始和已过期的记录
        bannerSet.removeIf(this::verifyInvalid);
        // currency 过滤
        bannerSet.removeIf(sysHomeBannerEntity ->
                sysHomeBannerEntity.getCurrencyFlag() == 1 && !currentCurrency.equalsIgnoreCase(sysHomeBannerEntity.getCurrency())
        );
        // survey 过滤
        bannerSet.removeIf(sysHomeBannerEntity -> verifySurveySelect(userId, sysHomeBannerEntity));
        // trackingMenopause 过滤
        Integer trackingMenopause = appUserInfo.getTrackingMenopause();
        bannerSet.removeIf(sysHomeBannerEntity -> verifyTrackingMenopause(trackingMenopause, sysHomeBannerEntity));
        // condition、age 过滤
        bannerSet.removeIf(sysHomeBannerEntity -> filterCondition(appUserInfo, sysHomeBannerEntity));
        bannerSet.removeIf(sysHomeBannerEntity -> filterAge(appUserInfo, sysHomeBannerEntity));
        // clinic 绑定过滤
        bannerSet.removeIf(sysHomeBannerEntity -> filterClinic(email, sysHomeBannerEntity));
        // orders 排序展示处理
        List<SysHomeBannerEntity> sortedDisplayList = bannerSet.stream()
                .sorted((Comparator.comparing(SysHomeBannerEntity::getOrders, Comparator.nullsLast(Comparator.naturalOrder()))))
                .collect(Collectors.toList());

        List<HomeBannerVO> homeBannerVOS = new ArrayList<>();
        for (SysHomeBannerEntity homeBannerEntity : sortedDisplayList) {
            HomeBannerVO homeBannerVO = new HomeBannerVO();
            BeanUtil.copyProperties(homeBannerEntity, homeBannerVO);
            String stories = homeBannerEntity.getStories();
            if (StringUtils.isNotBlank(stories)) {
                homeBannerVO.setStories(stories.charAt(0) == '['
                        ? JsonUtil.toArray(stories, Object.class)
                        : JsonUtil.toObject(stories, Object.class));
            }
            homeBannerVOS.add(homeBannerVO);
        }
        return homeBannerVOS;
    }

    private boolean verifyTrackingMenopause(Integer trackingMenopause, SysHomeBannerEntity sysHomeBannerEntity) {
        Integer bannerTrackingMenopause = sysHomeBannerEntity.getTrackingMenopause();
        if (bannerTrackingMenopause == null || bannerTrackingMenopause == -1) {
            return false;
        }
        if (trackingMenopause == null && bannerTrackingMenopause == 0) {
            return false;
        }
        return !bannerTrackingMenopause.equals(trackingMenopause);
    }

    private Set<SysHomeBannerEntity> filterByUserGoal(Integer goalStatus) {
        List<SysHomeBannerEntity> homeBannerEntityList = sysHomeBannerDAO.listByActive();

        UserGoalEnum userGoalEnum = UserGoalEnum.get(goalStatus);
        if (userGoalEnum == null) {
            return new HashSet<>(homeBannerEntityList);
        }

        return homeBannerEntityList.stream()
                .filter(homeBanner -> homeBanner.getUserTypes().contains(String.valueOf(userGoalEnum.getValue()))
                        || homeBanner.getUserTypes().contains(BannerUserTypeEnum.ALL.getValue())).collect(Collectors.toSet());
    }

    private boolean verifySurveySelect(Long userId, SysHomeBannerEntity bannerEntity) {
        String surveyIdsStr = bannerEntity.getSurveyIdsStr();
        if (StringUtils.isBlank(surveyIdsStr)) {
            return false;
        }
        List<String> surveyIds = StringListUtil.strToList(surveyIdsStr, ",");
        return surveyProvider.verifyBannerSurveySelect(userId, surveyIds).getData();
    }

    private boolean verifyInvalid(SysHomeBannerEntity bannerEntity) {
        String timeZone = "America/Los_Angeles";
        long currentTime = System.currentTimeMillis();
        // 未激活
        Predicate<SysHomeBannerEntity> conditionInActive = (banner) -> banner.getStatus().equals(0);
        // 未开始
        Predicate<SysHomeBannerEntity> conditionNotStart = (banner) -> {
            long startTime = ZoneDateUtil.timestamp(timeZone, banner.getStartTime(), DatePatternConst.DATE_TIME_PATTERN);
            return currentTime < startTime;
        };
        // 已过期
        Predicate<SysHomeBannerEntity> conditionExpire = (banner) -> {
            long endTime = ZoneDateUtil.timestamp(timeZone, banner.getEndTime(), DatePatternConst.DATE_TIME_PATTERN);
            return currentTime > endTime;
        };

        boolean result = conditionInActive.test(bannerEntity);
        if (StringUtils.isNotEmpty(bannerEntity.getStartTime())) {
            result = result || conditionNotStart.test(bannerEntity);
        }
        if (StringUtils.isNotEmpty(bannerEntity.getEndTime())) {
            result = result || conditionExpire.test(bannerEntity);
        }

        return result;
    }

    private boolean filterCondition(AppUserInfoEntity appUserInfo, SysHomeBannerEntity bannerEntity) {
        // condition
        String conditions = bannerEntity.getConditions();
        String userConditions = appUserInfo.getConditions();
        boolean filterCondition = false;
        if (StringUtils.isNotBlank(conditions)) {
            List<Integer> conditionList = StringListUtil.strToIntegerList(conditions, ",");
            if (StringUtils.isBlank(userConditions)) {
                filterCondition = true;
            } else {
                List<Integer> userConditionList = StringListUtil.strToIntegerList(userConditions, ",");
                List<Integer> intersection = conditionList.stream()
                        .filter(userConditionList::contains)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(intersection)) {
                    filterCondition = true;
                }
            }
        }
        return filterCondition;
    }

    private boolean filterAge(AppUserInfoEntity appUserInfo, SysHomeBannerEntity bannerEntity) {
        // age
        String age = bannerEntity.getAge();
        Integer userAge = AgeUtil.calculateAge(appUserInfo.getBirthYear(), appUserInfo.getBirthMonth(), appUserInfo.getBirthOfDay());
        boolean filterAge = false;
        if (StringUtils.isNotBlank(age)) {
            List<Integer> ageList = StringListUtil.strToIntegerList(age, ",");
            filterAge = !ageList.contains(getConditionNumber(userAge));
        }

        return filterAge;
    }

    private boolean filterClinic(String email, SysHomeBannerEntity bannerEntity) {
        String clinics = bannerEntity.getClinics();
        if (StringUtils.isNotBlank(clinics)) {
            List<Long> clinicIdList = JsonUtil.toArray(clinics, HomeBannerDTO.ClinicDTO.class)
                    .stream().map(HomeBannerDTO.ClinicDTO::getId).collect(Collectors.toList());
            try {
                List<UserClinicDTO> userBindClinicList = clinicProvider.listClinicInfos(email).getData();
                if (CollectionUtils.isEmpty(userBindClinicList)) {
                    return true;
                }
                List<Long> userBindClinicIdList = userBindClinicList.stream()
                        .map(UserClinicDTO::getId)
                        .collect(Collectors.toList());
                if (new HashSet<>(clinicIdList).containsAll(userBindClinicIdList)) {
                    return false;
                }
            } catch (Exception e) {
                log.error("clinic server error", e);
            }
        }

        return false;
    }

    private int getConditionNumber(Integer age) {
        if (age == null || age < 18) {
            return -1;
        } else if (age <= 25) {
            return 1;
        } else if (age <= 30) {
            return 2;
        } else if (age <= 35) {
            return 3;
        } else if (age <= 40) {
            return 4;
        } else if (age <= 45) {
            return 5;
        } else if (age <= 50) {
            return 6;
        } else {
            return 7;
        }
    }

    @Deprecated
    @Override
    public HomeDataVO homeData() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        HomeDataVO homeDataVO = new HomeDataVO();
        // 当天及前后三天
        long currentTimeMillis = System.currentTimeMillis();
        String today = ZoneDateUtil.format(timeZone, currentTimeMillis, DatePatternConst.DATE_PATTERN);
        List<String> dates = Arrays.asList(LocalDateUtil.plusDay(today, -3, DatePatternConst.DATE_PATTERN),
                LocalDateUtil.plusDay(today, -2, DatePatternConst.DATE_PATTERN),
                LocalDateUtil.plusDay(today, -1, DatePatternConst.DATE_PATTERN),
                today,
                LocalDateUtil.plusDay(today, 1, DatePatternConst.DATE_PATTERN),
                LocalDateUtil.plusDay(today, 2, DatePatternConst.DATE_PATTERN),
                LocalDateUtil.plusDay(today, 3, DatePatternConst.DATE_PATTERN));

        // 用户的日记选项配置
        CustomLogConfigDTO customLogConfigDTO = userCustomLogManager.configInfo(userId);
        String tempUnit = customLogConfigDTO.getTempUnit();
        homeDataVO.setTempUnit(tempUnit);

        // 用户算法结果数据
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getAlgorithmResultCheckPeriod(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);

        // 首页每天数据
        List<HomeDailyDataVO> homeDailyDataVOS = new ArrayList<>();
        List<WandTestDataDTO> wandDayTestDataDTOS = new ArrayList<>();
        for (String date : dates) {
            List<HormoneDTO> dailyHormoneDatas = hormoneDatas.stream()
                    .filter(hormoneData -> date.equals(LocalDateUtil.dateTime2Date(hormoneData.getTest_time())))
                    .collect(Collectors.toList());
            WandTestDataDTO wandDayTestDataDTO = new WandTestDataDTO();
            wandDayTestDataDTO.setDate(date);
            wandDayTestDataDTO.setHormoneDTO(dailyHormoneDatas);
            wandDayTestDataDTOS.add(wandDayTestDataDTO);
        }
        Map<String, List<WandTestBiomarkerDTO>> wandTestBiomarkerMap = bluetoothProvider.getDayWandBiomarkerData(wandDayTestDataDTOS, userId).getData()
                .stream().collect(Collectors.toMap(WandDiaryTestBiomarkerDTO::getDate, WandDiaryTestBiomarkerDTO::getWandTestBiomarkerDTOS));

        for (String date : dates) {
            HomeDailyDataVO homeDailyDataVO = new HomeDailyDataVO();
            homeDailyDataVO.setDate(date);
            List<WandTestBiomarkerDTO> wandTestBiomarkerDTOS = wandTestBiomarkerMap.get(date);

            // BBT
            if (LocalDateUtil.minusToDay(date, today) <= 0) {
                List<TemperatureDTO> temperatureDTOS = userDiaryLogManager.listTemperatureDTO(userId, date);
                List<TemperatureDTO> filterTemperatureDTOS = temperatureDTOS.stream()
                        .filter(temperatureDTO -> temperatureDTO.getType() != 0)
                        .filter(temperatureDTO -> StringUtils.isBlank(temperatureDTO.getEcode()))
                        .collect(Collectors.toList());
                for (TemperatureDTO temperatureDTO : filterTemperatureDTOS) {
                    WandTestBiomarkerDTO wandTestBiomarkerDTO = new WandTestBiomarkerDTO("BBT", temperatureDTO.getTempTime(), temperatureDTO.getEcode());
                    String testValue;
                    if (TempUnitEnum.C.getValue().equals(tempUnit)) {
                        testValue = temperatureDTO.getTempC().setScale(2, RoundingMode.HALF_UP).floatValue() + "";
                    } else {
                        testValue = temperatureDTO.getTempF().setScale(2, RoundingMode.HALF_UP).floatValue() + "";
                    }
                    wandTestBiomarkerDTO.setTestValue(testValue);
                    wandTestBiomarkerDTOS.add(wandTestBiomarkerDTO);
                }
            }
            homeDailyDataVO.setHormones(wandTestBiomarkerDTOS);

            Integer cycleStatus = -1;
            for (CycleDataDTO CycleDataDTO : cycleDataDTOS) {
                String datePeriodStart = CycleDataDTO.getDate_period_start();
                Integer lenCycle = CycleDataDTO.getLen_cycle();
                String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, lenCycle, DatePatternConst.DATE_PATTERN);
                if (LocalDateUtil.minusToDay(date, datePeriodStart) < 0
                        || LocalDateUtil.minusToDay(date, dateCycleEnd) >= 0) {
                    cycleStatus = -1;
                    continue;
                }
                cycleStatus = CycleDataDTO.getCycle_status();
                homeDailyDataVO.setLenCycle(lenCycle);
                homeDailyDataVO.setCycleStatus(CycleDataDTO.getCycle_status());
                homeDailyDataVO.setDatePeriodStart(datePeriodStart);
                homeDailyDataVO.setDatePeriodEnd(CycleDataDTO.getDate_period_end());
                homeDailyDataVO.setDateFwStart(CycleDataDTO.getDate_FW_start());
                homeDailyDataVO.setDateFwEnd(CycleDataDTO.getDate_FW_end());
                homeDailyDataVO.setDateOvulation(CycleDataDTO.getDate_ovulation());
                homeDailyDataVO.setLen_phase(CycleDataDTO.getLen_phase());
                Integer dayInCycle = LocalDateUtil.minusToDay(date, datePeriodStart) + 1;
                homeDailyDataVO.setDayInCycle(dayInCycle);
                List<Float> fertilityScoreList = CycleDataDTO.getFertility_score_list();
                if (CollectionUtils.isNotEmpty(fertilityScoreList)) {
                    homeDailyDataVO.setFertilityScore(fertilityScoreList.get(homeDailyDataVO.getDayInCycle() - 1));
                }
                PregnantRiskVO pregnantRisk = new PregnantRiskVO();
                PregnantRiskDTO pregnant_risk = CycleDataDTO.getPregnant_risk();
                if (pregnant_risk != null) {
                    pregnantRisk.setHighRiskCDs(pregnant_risk.getHigh_risks());
                    pregnantRisk.setMediumRiskCDs(pregnant_risk.getMedium_risks());
                    pregnantRisk.setLowRiskCDs(pregnant_risk.getLow_risks());
                }
                homeDailyDataVO.setPregnantRisk(pregnantRisk);

                break;
            }

            if (LocalDateUtil.minusToDay(date, today) >= 0) {
                DayTestProductsDTO dayTestProductsDTO = new DayTestProductsDTO();
                dayTestProductsDTO.setDate(date);
                dayTestProductsDTO.setCycleDataDTOS(cycleDataDTOS);
                dayTestProductsDTO.setWandTestBiomarkerDTOS(wandTestBiomarkerDTOS);
                List<TestRemindDTO> testingDTOS = TestRemindUtil.getTestRemind(dayTestProductsDTO);
                homeDailyDataVO.setTestingDTOS(testingDTOS);
            }

            HomeShowWordDTO showWordDTO = null;
            boolean contains = List.of(CycleStatusEnum.REAL_CYCLE.getStatus(),
                            CycleStatusEnum.FORECAST_CYCLE.getStatus(),
                            CycleStatusEnum.PREGNANCY_CYCLE_FIRST_STAGE.getStatus(),
                            CycleStatusEnum.PREGNANCY_CYCLE_SECOND_STAGE.getStatus(),
                            CycleStatusEnum.PREGNANCY_CYCLE_THIRD_STAGE.getStatus(),
                            CycleStatusEnum.PREGNANCY_CYCLE.getStatus(),
                            CycleStatusEnum.ABNORMAL_PREGNANCY_CYCLE.getStatus()
                    )
                    .contains(cycleStatus);
            if (contains) {
                showWordDTO = CycleShowWordUtil.buildShowWord(cycleDataDTOS, date, today);
            }
            homeDailyDataVO.setShowWordDTO(showWordDTO);

            // 如果date在最后一个周期的最后一天之后，需要补空周期数据，这个时候cycleStatus=-1
            if (cycleStatus == -1) {
                CycleDataDTO lastCycleDataDTO = cycleDataDTOS.get(cycleDataDTOS.size() - 1);
                Integer lastLenCycle = lastCycleDataDTO.getLen_cycle();
                String dateNewPeriodStart = LocalDateUtil.plusDay(lastCycleDataDTO.getDate_period_start(), lastLenCycle, DatePatternConst.DATE_PATTERN);
                homeDailyDataVO.setCycleStatus(CycleStatusEnum.EMPTY_CYCLE.getStatus());
                homeDailyDataVO.setDatePeriodStart(dateNewPeriodStart);
                int index = dates.indexOf(date);//index in [0,6]
                int subtract = LocalDateUtil.minusToDay(date, dateNewPeriodStart);
                Integer newLenCycle = subtract + 7 - index;
                homeDailyDataVO.setLenCycle(newLenCycle);
                homeDailyDataVO.setDayInCycle(subtract + 1);
                showWordDTO = new HomeShowWordDTO();
                showWordDTO.setCode(HomeShowWordEnum.CYCLE.getValue());
                //                showWordDTO.setValue(subtract + 1);
                showWordDTO.setValues(Arrays.asList(subtract + 1));
                homeDailyDataVO.setShowWordDTO(showWordDTO);
            }

            homeDailyDataVOS.add(homeDailyDataVO);
        }
        Map<String, CustomlogExistVO> customlogExistVOMap = getCustomlogExistVO(userId, dates, today);
        for (Map.Entry<String, CustomlogExistVO> customlogExistVOEntry : customlogExistVOMap.entrySet()) {
            String date = customlogExistVOEntry.getKey();
            HomeDailyDataVO homeDailyDataVO = homeDailyDataVOS.stream().filter(homeDailyData -> homeDailyData.getDate().equals(date)).findFirst().get();
            homeDailyDataVO.setCustomlogExistVO(customlogExistVOEntry.getValue());
        }
        homeDataVO.setHomeDailyDataVOS(homeDailyDataVOS);

        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        homeDataVO.setUserMode(UserGoalUtil.getUserGoalEnum(loginUserInfoDTO).getValue());
        homeDataVO.setRemindFlag(loginUserInfoDTO.getRemindFlag());
        if (!hormoneDatas.isEmpty()) {
            homeDataVO.setBindLogFlag(1);
        } else {
            List<AppUserBindLogEntity> bindLogEntities = appUserBindLogDAO.list10BindLog(userId);
            if (CollectionUtils.isEmpty(bindLogEntities)) {
                homeDataVO.setBindLogFlag(0);
            } else {
                homeDataVO.setBindLogFlag(1);
            }
        }

        return homeDataVO;
    }

    private Map<String, CustomlogExistVO> getCustomlogExistVO(Long userId, List<String> dates, String today) {
        Map<String, CustomlogExistVO> customlogExistVOMap = new HashMap<>();
        List<String> pastDates = dates.stream()
                .filter(date -> LocalDateUtil.minusToDay(date, today) <= 0)
                .collect(Collectors.toList());

        List<AppUserDiaryEntity> appUserDiaryEntities = appUserDiaryDAO.listByUserIdInDayStr(userId, pastDates);
        List<AppUserDiarySymptomsEntity> appUserDiarySymptomsEntities = appUserDiarySymptomsDAO.listByUserIdInDayStr(userId, pastDates);
        List<AppUserDiaryMoodsEntity> appUserDiaryMoodsEntities = appUserDiaryMoodsDAO.listByUserIdInDay(userId, pastDates);
        List<UserDiaryMoodsDTO> appUserDiaryMoodsDTOS = new ArrayList<>();
        for (AppUserDiaryMoodsEntity appUserDiaryMoodsEntity : appUserDiaryMoodsEntities) {
            if (StringUtils.isBlank(appUserDiaryMoodsEntity.getMood())
                    && StringUtils.isBlank(appUserDiaryMoodsEntity.getMoodInterfering())
                    && StringUtils.isBlank(appUserDiaryMoodsEntity.getFeeling())
                    && StringUtils.isBlank(appUserDiaryMoodsEntity.getSexDrive())
                    && StringUtils.isBlank(appUserDiaryMoodsEntity.getProductivity())
                    && StringUtils.isBlank(appUserDiaryMoodsEntity.getCravings())
                    && StringUtils.isBlank(appUserDiaryMoodsEntity.getExercise())
                    && StringUtils.isBlank(appUserDiaryMoodsEntity.getSkin())
                    && StringUtils.isBlank(appUserDiaryMoodsEntity.getLooks())
            ) {
                continue;
            }
            UserDiaryMoodsDTO appUserDiaryMoodsDTO = new UserDiaryMoodsDTO();
            BeanUtil.copyProperties(appUserDiaryMoodsEntity, appUserDiaryMoodsDTO);
            appUserDiaryMoodsDTOS.add(appUserDiaryMoodsDTO);
        }

        for (String date : dates) {
            CustomlogExistVO customlogExistVO = new CustomlogExistVO();
            if (LocalDateUtil.minusToDay(date, today) > 0) {
                customlogExistVO.setType(0);
                customlogExistVO.setContent("");
                customlogExistVOMap.put(date, customlogExistVO);
                continue;
            }
            boolean exist = CollectionUtils.isNotEmpty(appUserDiaryEntities)
                    && appUserDiaryEntities.stream()
                    .anyMatch(appUserDiaryEntity -> appUserDiaryEntity.getDiaryDayStr().equals(date));
            if (CollectionUtils.isNotEmpty(appUserDiarySymptomsEntities)
                    && appUserDiarySymptomsEntities.stream()
                    .anyMatch(appUserDiarySymptomsEntity -> appUserDiarySymptomsEntity.getDiaryDayStr().equals(date))) {
                exist = true;
            }
            if (CollectionUtils.isNotEmpty(appUserDiaryMoodsDTOS)
                    && appUserDiaryMoodsDTOS.stream()
                    .anyMatch(appUserDiaryMoodsVO -> appUserDiaryMoodsVO.getDiaryDayStr().equals(date))) {
                exist = true;
            }
            if (exist) {
                customlogExistVO.setType(0);
                customlogExistVO.setContent("");
            } else {
                customlogExistVO.setType(1);
                if (date.equals(today)) {
                    customlogExistVO.setContent("Add your logs for today");
                } else {
                    customlogExistVO.setContent("Add your logs for this day");
                }
            }
            customlogExistVOMap.put(date, customlogExistVO);
        }

        return customlogExistVOMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void extendPeriod() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        extendPeriodManager.extendPeriod(timeZone, userId, algorithmResultDTO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void confirmPeriod() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);

        // entity check
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            throw new UserException("user period not exist");
        }

        // algorithm result
        AlgorithmResultDTO algorithmResultDTO = cacheManager.getCacheAlgorithmResult(userId);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        CycleDataDTO thisCycleDataDTO = null;
        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            if (CycleStatusEnum.FORECAST_CYCLE.getStatus() != cycleDataDTO.getCycle_status()) {
                continue;
            }
            String datePeriodStart = cycleDataDTO.getDate_period_start();
            String dateCycleEnd = LocalDateUtil.plusDay(datePeriodStart, cycleDataDTO.getLen_cycle(), DatePatternConst.DATE_PATTERN);
            int subtractFromPeriodStart = LocalDateUtil.minusToDay(today, datePeriodStart);
            int subtractFromCycleEnd = LocalDateUtil.minusToDay(today, dateCycleEnd);
            if (subtractFromPeriodStart < 0 || subtractFromCycleEnd >= 0) {
                continue;
            }
            thisCycleDataDTO = cycleDataDTO;
        }
        if (thisCycleDataDTO == null) {
            log.info("user:{} don't need to confirmPeriod", userId);
            return;
        }

        String datePeriodStart = thisCycleDataDTO.getDate_period_start();
        String datePeriodEnd = thisCycleDataDTO.getDate_period_end();
        List<String> twoDaysDay = LocalDateUtil.getBetweenDate(datePeriodStart, datePeriodEnd);
        List<Long> dbPeriodList = StringListUtil.strToLongList(appUserPeriodEntity.getPeriods(), ",");
        List<Long> periodList = new ArrayList<>();
        if (!dbPeriodList.isEmpty()) {
            periodList.addAll(dbPeriodList);
        }
        for (String date : twoDaysDay) {
            Long dateLong = ZoneDateUtil.timestamp(timeZone, date, DatePatternConst.DATE_PATTERN);
            periodList.add(dateLong);
        }

        // build user period data
        List<Long> sortedPeriodList = periodList.stream().sorted(Long::compareTo).collect(Collectors.toList());
        String periodsStr = StringListUtil.listToString(sortedPeriodList, ",");
        List<UserPeriodDataDTO> userPeriodDataDTOS = CallEditPeriodUtil.buildUserPeriodDataDTOS(timeZone, sortedPeriodList,
                appUserPeriodEntity.getCutPoints(), appUserPeriodEntity.getAvgLenPeriod());

        // update
        String periodListJson = JsonUtil.toJson(userPeriodDataDTOS);
        appUserPeriodEntity.setPeriods(StringUtils.isBlank(periodsStr) ? "" : periodsStr);
        appUserPeriodEntity.setPeriodData(periodListJson);
        appUserPeriodEntity.setModifier(-1L);
        UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appUserPeriodEntity);
        appUserPeriodDAO.updateById(appUserPeriodEntity);

        // build period info
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        UserPeriodParamDTO userPeriodParamDTO = CallEditPeriodUtil.buildUserPeriodParamDTO(loginUserInfoDTO, appUserPeriodEntity, userPeriodDataDTOS);

        // call algorithm
        PrepareEditPeriodDTO prepareEditPeriodDTO = CallEditPeriodUtil.buildPrepareEditPeriodDTO(timeZone,
                loginUserInfoDTO.getEmail(), appUserPeriodEntity.getPeriods(), userPeriodParamDTO);
        algorithmCallManager.editPeriod(prepareEditPeriodDTO, AlgorithmRequestTypeEnum.CONFIRM_PERIOD);

        // clinic
        patientProducer.periodChange(userId, AlgorithmRequestTypeEnum.CONFIRM_PERIOD);

        // cache
        cacheManager.deleteUserDetailCache(userId);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void longerCycleChange() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        log.info("longer-cycle-change userId:{} today:{}", userId, today);
        AppUserPeriodEntity appUserPeriodEntity = appUserPeriodDAO.getByUserId(userId);
        if (appUserPeriodEntity == null) {
            throw new UserException("user period not exist");
        }

        // call algotirhm
        LoginUserInfoDTO loginUserInfoDTO = ssoProvider.getUserLoginInfo(userId).getData();
        algorithmCallManager.lognerPeriod(appUserPeriodEntity, loginUserInfoDTO);
    }
}
