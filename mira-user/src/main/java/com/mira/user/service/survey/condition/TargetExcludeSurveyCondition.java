package com.mira.user.service.survey.condition;

import com.mira.api.mongo.dto.SurveyCondition;
import com.mira.api.mongo.provider.ISurveyProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Exclude collected survey: even if user passed only 1 survey - they should be excluded
 * Include collected survey: if only 1 passed it should be included
 * targetSurveyIds or excludeSurveyIds, not allow to exist both
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2024-07-22
 **/
@Slf4j
@Service
public class TargetExcludeSurveyCondition {
    @Resource
    private ISurveyProvider surveyProvider;

    public boolean checkTargetExcludeSurvey(Long userId, SurveyCondition surveyCondition) {
        List<String> targetSurveyIds = surveyCondition.getTargetSurveyIds();
        List<String> excludeSurveyIds = surveyCondition.getExcludeSurveyIds();

        if (targetSurveyIds.isEmpty() && excludeSurveyIds.isEmpty()) {
            return true;
        }
        Integer surveyCheckType = null;
        List<String> existSurveyIds = null;
        if (!targetSurveyIds.isEmpty()) {
            surveyCheckType = 0;
            existSurveyIds = targetSurveyIds;
        } else {
            surveyCheckType = 1;
            existSurveyIds = excludeSurveyIds;
        }

        return surveyProvider.verifyBindSurveyIds(userId, surveyCheckType, existSurveyIds).getData();
    }
}
