package com.mira.user.service.hormone;

import com.mira.api.bluetooth.dto.hormone.DataHistoryDTO;
import com.mira.api.bluetooth.dto.hormone.TestDateDTO;
import com.mira.core.request.PageDTO;
import com.mira.mybatis.response.PageResult;
import com.mira.user.controller.vo.hormone.UserDataManualVO;
import com.mira.api.bluetooth.dto.hormone.ManualDataDTO;

/**
 * @program: mira_server_microservices
 * @description: 荷尔蒙接口
 * @author: xizhao.dai
 * @create: 2024-07-31 13:27
 **/
public interface IHormoneService {
    /**
     * 添加测试数据
     *
     * @param manualDataDTO 测试数据
     */
    void manualAddData(ManualDataDTO manualDataDTO);

    /**
     * 用户测试数据分页列表
     *
     * @param pageParam
     * @return
     */
    PageResult<UserDataManualVO> manualDataPage(PageDTO pageParam);

    /**
     * 获取某一天的测试历史
     *
     * @param dateStr
     * @return
     */
    DataHistoryDTO getHistory(String dateStr);

    /**
     * 获取下一个测试日
     *
     * @return
     */
    TestDateDTO getNextTestDate();
}