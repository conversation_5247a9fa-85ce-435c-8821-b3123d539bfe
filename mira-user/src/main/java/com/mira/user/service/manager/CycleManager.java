package com.mira.user.service.manager;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.*;
import com.mira.api.bluetooth.dto.menopause.MenopauseResultDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.bluetooth.enums.CycleStatusEnum;
import com.mira.api.bluetooth.provider.IMenopauseProvider;
import com.mira.api.sso.dto.LoginUserInfoDTO;
import com.mira.api.user.enums.UserGoalEnum;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.*;
import com.mira.user.controller.vo.chart.PregnantRiskVO;
import com.mira.user.controller.vo.cycle.CycleAnalysisVO;
import com.mira.user.dal.dao.AppUserPeriodDAO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 周期接口通用层
 *
 * <AUTHOR>
 */
@Component
public class CycleManager {
    @Resource
    private IMenopauseProvider menopauseProvider;
    @Resource
    private AppUserPeriodDAO appUserPeriodDAO;

    public CycleAnalysisVO buildCycleAnalysisVO(LoginUserInfoDTO loginUserInfoDTO,
                                                AlgorithmResultDTO algorithmResultDTO,
                                                List<Long> sexDateList) {
        CycleAnalysisDTO cycleAnalysis = JsonUtil.toObject(algorithmResultDTO.getCycleAnalysis(), CycleAnalysisDTO.class);
        List<CycleDataDTO> cycleDataDTOS = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);

        Integer ttaSwitch = loginUserInfoDTO.getTtaSwitch();
        Integer goalStatus = loginUserInfoDTO.getGoalStatus();
        Integer userMode = UserGoalEnum.getUserMode(ttaSwitch, goalStatus);
        Integer trackingMenopause = loginUserInfoDTO.getTrackingMenopause();

        return buildCycleAnalysisVO(loginUserInfoDTO.getUserId(), userMode,
                trackingMenopause, cycleAnalysis, cycleDataDTOS, hormoneDatas, sexDateList);
    }

    public CycleAnalysisVO buildCycleAnalysisVO(Long userId,
                                                Integer userMode,
                                                Integer trackingMenopause,
                                                CycleAnalysisDTO cycleAnalysis,
                                                List<CycleDataDTO> cycleDataDTOS,
                                                List<HormoneDTO> hormoneDatas,
                                                List<Long> sexDateList) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // 初始化返回对象并设置基础信息
        CycleAnalysisVO cycleAnalysisVO = new CycleAnalysisVO();
        cycleAnalysisVO.setUserMode(userMode);
        cycleAnalysisVO.setTrackingMenopause(trackingMenopause == null ? 0 : trackingMenopause);

        // 处理更年期跟踪相关数据
        if (trackingMenopause != null && trackingMenopause == 1) {
            processMenopauseData(userId, cycleAnalysisVO, hormoneDatas, timeZone);
        }

        // 设置周期分析基础数据
        if (cycleAnalysis != null) {
            setCycleAnalysisData(cycleAnalysisVO, cycleAnalysis);
            setCycleAnalysisDataV2(cycleAnalysisVO, cycleAnalysis);
        }

        // 构建周期数据列表
        List<CycleAnalysisVO.CycleData> analysisCycleData = buildCycleDataList(cycleDataDTOS, sexDateList, timeZone);

        // 倒序排列周期数据
        if (CollectionUtils.isNotEmpty(analysisCycleData)) {
            Collections.reverse(analysisCycleData);
        }
        cycleAnalysisVO.setCycleData(analysisCycleData);

        return cycleAnalysisVO;
    }

    /**
     * 处理更年期相关数据
     */
    private void processMenopauseData(Long userId, CycleAnalysisVO cycleAnalysisVO,
                                      List<HormoneDTO> hormoneDatas, String timeZone) {
        // 获取更年期结果并设置阶段
        MenopauseResultDTO menopauseResultDTO = menopauseProvider.getMenopauseResult(userId,
                AlgorithmRequestTypeEnum.GET_MENOPAUSE_RESULT_buildCycleAnalysisVO).getData();
        cycleAnalysisVO.setMenopauseStage(menopauseResultDTO.getDefineStage());

        // 查找最新的FSH激素数据
        HormoneDTO lastFshHormoneDTO = hormoneDatas.stream()
                .filter(hormoneDTO -> WandTypeEnum.FSH.getInteger().equals(hormoneDTO.getTest_results().getWand_type()))
                .max(Comparator.comparing(HormoneDTO::getTest_time))
                .orElse(null);

        if (lastFshHormoneDTO != null) {
            TestDataDTO lastFsh = new TestDataDTO();
            lastFsh.setTestTime(lastFshHormoneDTO.getTest_time());
            lastFsh.setValue(lastFshHormoneDTO.getTest_results().getValue1());
            cycleAnalysisVO.setLastFsh(lastFsh);
        }

        // 获取最后一次经期日期
        String lastPeriodDate = appUserPeriodDAO.getLastPeriodDate(userId, timeZone);
        cycleAnalysisVO.setLastMenstrualPeriodDate(lastPeriodDate);
    }

    /**
     * 设置周期分析基础数据
     */
    private void setCycleAnalysisData(CycleAnalysisVO cycleAnalysisVO, CycleAnalysisDTO cycleAnalysis) {
        cycleAnalysisVO.setCycleLength(cycleAnalysis.getCycle_len());
        cycleAnalysisVO.setPeriodLength(cycleAnalysis.getPeriod_len());
        cycleAnalysisVO.setLutealPhases(cycleAnalysis.getLuteal_phases());
        cycleAnalysisVO.setFolicularPhases(cycleAnalysis.getFolicular_phases());

        Integer ovulationEstimate = cycleAnalysis.getOvulation_estimate();
        cycleAnalysisVO.setOvulationEstimate(ovulationEstimate);
        if (ovulationEstimate != null) {
            cycleAnalysisVO.setOvulationEstimateUnit(DaySuffixUtil.getDaySuffix(ovulationEstimate));
        }
    }

    /**
     * 设置周期分析基础数据v2
     */
    private void setCycleAnalysisDataV2(CycleAnalysisVO cycleAnalysisVO, CycleAnalysisDTO cycleAnalysis) {
        BeanUtil.copyProperties(cycleAnalysis, cycleAnalysisVO, CopyOptionsUtil.getCopyOptions(CycleAnalysisDTO.class));

        Integer ovulationEstimateV2 = cycleAnalysis.getOvulation_estimate_v2();
        cycleAnalysisVO.setOvulationEstimateV2(ovulationEstimateV2);
        if (ovulationEstimateV2 != null) {
            cycleAnalysisVO.setOvulationEstimateUnitV2(DaySuffixUtil.getDaySuffix(ovulationEstimateV2));
        }
    }

    /**
     * 构建周期数据列表
     */
    private List<CycleAnalysisVO.CycleData> buildCycleDataList(List<CycleDataDTO> cycleDataDTOS,
                                                               List<Long> sexDateList, String timeZone) {
        List<CycleAnalysisVO.CycleData> analysisCycleData = new ArrayList<>();

        for (CycleDataDTO cycleDataDTO : cycleDataDTOS) {
            // 只处理真实周期数据
            if (CycleStatusEnum.REAL_CYCLE.getStatus() != cycleDataDTO.getCycle_status()) {
                continue;
            }

            // 验证必要的日期数据
            if (!isValidCycleData(cycleDataDTO)) {
                continue;
            }

            CycleAnalysisVO.CycleData cycleDataVO = buildSingleCycleData(cycleDataDTO, sexDateList, timeZone);
            analysisCycleData.add(cycleDataVO);
        }

        return analysisCycleData;
    }

    /**
     * 验证周期数据的有效性
     */
    private boolean isValidCycleData(CycleDataDTO cycleDataDTO) {
        String datePeriodStart = cycleDataDTO.getDate_period_start();
        String datePeriodEnd = cycleDataDTO.getDate_period_end();
        String dateFwStart = cycleDataDTO.getDate_FW_start();
        String dateFwEnd = cycleDataDTO.getDate_FW_end();

        return StringUtils.isNotBlank(datePeriodStart) && StringUtils.isNotBlank(datePeriodEnd) &&
                StringUtils.isNotBlank(dateFwStart) && StringUtils.isNotBlank(dateFwEnd);
    }

    /**
     * 构建单个周期数据
     */
    private CycleAnalysisVO.CycleData buildSingleCycleData(CycleDataDTO cycleDataDTO,
                                                           List<Long> sexDateList, String timeZone) {
        CycleAnalysisVO.CycleData cycleDataVO = new CycleAnalysisVO.CycleData();

        // 设置基础信息
        cycleDataVO.setCycleIndex(cycleDataDTO.getCycle_index());
        cycleDataVO.setLenCycle(cycleDataDTO.getLen_cycle());

        // 提取日期字符串
        String datePeriodStart = cycleDataDTO.getDate_period_start();
        String datePeriodEnd = cycleDataDTO.getDate_period_end();
        String dateFwStart = cycleDataDTO.getDate_FW_start();
        String dateFwEnd = cycleDataDTO.getDate_FW_end();
        String dateLhSurge = cycleDataDTO.getDate_LH_surge();
        List<String> datePdgRises = cycleDataDTO.getDate_PDG_rise();

        // 计算并设置各种日期索引
        cycleDataVO.setDatePeriodStart(datePeriodStart);
        cycleDataVO.setIndexPeriodEnd(LocalDateUtil.minusToDay(datePeriodEnd, datePeriodStart));
        cycleDataVO.setIndexFwStart(LocalDateUtil.minusToDay(dateFwStart, datePeriodStart));
        cycleDataVO.setIndexFwEnd(LocalDateUtil.minusToDay(dateFwEnd, datePeriodStart));

        // 设置LH峰值日期索引
        if (StringUtils.isNotBlank(dateLhSurge)) {
            cycleDataVO.setIndexLhSurgeDay(LocalDateUtil.minusToDay(dateLhSurge, datePeriodStart));
        } else {
            cycleDataVO.setIndexLhSurgeDay(null);
        }

        // 处理PDG上升日期索引
        cycleDataVO.setIndexPdgRises(buildPdgRiseIndexes(datePdgRises, datePeriodStart));

        // 处理性行为日期索引
        cycleDataVO.setIndexesOfSex(buildSexIndexes(sexDateList, datePeriodStart, cycleDataDTO.getLen_cycle(), timeZone));

        // 设置怀孕风险数据
        cycleDataVO.setPregnantRisk(buildPregnantRisk(cycleDataDTO.getPregnant_risk()));

        return cycleDataVO;
    }

    /**
     * 构建PDG上升日期索引列表
     */
    private List<Integer> buildPdgRiseIndexes(List<String> datePdgRises, String datePeriodStart) {
        List<Integer> indexPdgRises = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(datePdgRises)) {
            for (String datePdgRise : datePdgRises) {
                int indexPdgRise = LocalDateUtil.minusToDay(datePdgRise, datePeriodStart);
                indexPdgRises.add(indexPdgRise);
            }
        }
        return indexPdgRises;
    }

    /**
     * 构建性行为日期索引列表
     */
    private List<Integer> buildSexIndexes(List<Long> sexDateList, String datePeriodStart,
                                          Integer cycleLength, String timeZone) {
        List<Integer> sexIndexList = new ArrayList<>();
        if (sexDateList.isEmpty()) {
            return sexIndexList;
        }

        Long datePeriodStartTimestamp = ZoneDateUtil.timestamp(timeZone, datePeriodStart, DatePatternConst.DATE_PATTERN);

        sexDateList.forEach(sexDate -> {
            // 计算性行为日期与周期开始日期的天数差
            long betweenDays = (sexDate - datePeriodStartTimestamp) / (1000 * 3600 * 24);
            if (betweenDays >= 0 && betweenDays < cycleLength) {
                sexIndexList.add((int) betweenDays);
            }
        });

        return sexIndexList;
    }

    /**
     * 构建怀孕风险数据
     */
    private PregnantRiskVO buildPregnantRisk(PregnantRiskDTO pregnantRiskDTO) {
        PregnantRiskVO pregnantRisk = new PregnantRiskVO();
        if (pregnantRiskDTO != null) {
            pregnantRisk.setHighRiskCDs(pregnantRiskDTO.getHigh_risks());
            pregnantRisk.setMediumRiskCDs(pregnantRiskDTO.getMedium_risks());
            pregnantRisk.setLowRiskCDs(pregnantRiskDTO.getLow_risks());
        }
        return pregnantRisk;
    }
}
