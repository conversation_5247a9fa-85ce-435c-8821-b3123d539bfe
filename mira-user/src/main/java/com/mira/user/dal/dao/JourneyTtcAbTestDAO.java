package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.JourneyTtcAbTestEntity;
import com.mira.user.dal.mapper.JourneyTtcAbTestMapper;
import org.springframework.stereotype.Repository;

@Repository
public class JourneyTtcAbTestDAO extends ServiceImpl<JourneyTtcAbTestMapper, JourneyTtcAbTestEntity> {
    public long countByUserId(Long userId) {
        return count(Wrappers.<JourneyTtcAbTestEntity>lambdaQuery()
                .eq(JourneyTtcAbTestEntity::getUserId, userId));
    }
}
