package com.mira.user.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.user.dal.entity.SysArticlesEntity;
import com.mira.user.dal.mapper.SysArticlesMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * sys_articles DAO
 *
 * <AUTHOR>
 */
@Repository
public class SysArticlesDAO extends ServiceImpl<SysArticlesMapper, SysArticlesEntity> {
    public List<SysArticlesEntity> listByCycle(Integer cycleType) {
        LambdaQueryWrapper<SysArticlesEntity> queryWrapper = Wrappers.<SysArticlesEntity>lambdaQuery()
                .eq(SysArticlesEntity::getCycleType, cycleType)
                .orderByDesc(SysArticlesEntity::getId);
        return list(queryWrapper);
    }

    public List<SysArticlesEntity> listByCycleAndPhase(Integer cycleType, Integer phaseType) {
        LambdaQueryWrapper<SysArticlesEntity> queryWrapper = Wrappers.<SysArticlesEntity>lambdaQuery()
                .eq(SysArticlesEntity::getCycleType, cycleType)
                .eq(SysArticlesEntity::getPhaseType, phaseType)
                .orderByDesc(SysArticlesEntity::getId);
        return list(queryWrapper);
    }

    public List<SysArticlesEntity> listByGoalAndCycleAndPhase(Integer goalType, Integer cycleType, Integer phaseType) {
        LambdaQueryWrapper<SysArticlesEntity> queryWrapper = Wrappers.<SysArticlesEntity>lambdaQuery()
                .eq(SysArticlesEntity::getGoalType, goalType)
                .eq(SysArticlesEntity::getCycleType, cycleType)
                .eq(SysArticlesEntity::getPhaseType, phaseType)
                .orderByDesc(SysArticlesEntity::getId);
        return list(queryWrapper);
    }
}
