package com.mira.upgrade.service.impl;

import com.mira.api.user.dto.user.AppUserInfoDTO;
import com.mira.api.user.provider.IUserProvider;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.upgrade.consts.UpgradeWayConst;
import com.mira.upgrade.controller.vo.FirmwareUpgradeResultVO;
import com.mira.upgrade.dal.dao.AppFirmwareUpgradeDAO;
import com.mira.upgrade.dal.dao.UserFirmwareBetaDAO;
import com.mira.upgrade.dal.entity.AppFirmwareUpgradeEntity;
import com.mira.upgrade.dal.entity.UserFirmwareBetaEntity;
import com.mira.upgrade.exception.UpgradeException;
import com.mira.upgrade.properties.UpgradeProperties;
import com.mira.upgrade.service.IFirmwareService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 固件升级接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class FirmwareServiceImpl implements IFirmwareService {
    @Resource
    private UserFirmwareBetaDAO userFirmwareBetaDAO;
    @Resource
    private AppFirmwareUpgradeDAO appFirmwareUpgradeDAO;
    @Resource
    private IUserProvider userProvider;
    @Resource
    private UpgradeProperties upgradeProperties;

    /**
     * 固件升级检查
     * 根据用户当前固件版本和用户类型（普通/Beta）确定是否需要升级以及升级方式
     *
     * @return 固件升级结果，包含升级标志、版本、下载链接和升级内容
     */
    @Override
    public FirmwareUpgradeResultVO upgrade() {
        // 1. 获取用户信息和当前绑定版本
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppUserInfoDTO userInfo = userProvider.getUserInfoById(userId).getData();
        String currentBindVersion = userInfo.getBindVersion();

        // 2. 检查用户是否绑定设备，未绑定则不升级
        if (StringUtils.isBlank(currentBindVersion)) {
            return noUpgradeResult(null);
        }

        // 3. 格式化版本号并提取主版本和子版本
        String formattedVersion = formatVersionNumber(currentBindVersion);
        String mainVersion = formattedVersion.substring(0, 5);  // 例如："01.07"
        String subVersion = formattedVersion.substring(6, 11);  // 例如："00.00"
        log.debug("user {} current firmware version: {}, main version: {}, sub version: {}", userId, formattedVersion, mainVersion, subVersion);

        // 4. 确定用户类型（Beta或普通用户）并获取对应的固件升级列表
        UserFirmwareBetaEntity betaUserInfo = userFirmwareBetaDAO.getByUserId(userId);
        boolean isBetaUser = ObjectUtils.isNotEmpty(betaUserInfo);
        List<AppFirmwareUpgradeEntity> availableUpgrades = isBetaUser ?
                appFirmwareUpgradeDAO.listByBeta() :
                appFirmwareUpgradeDAO.listByNoBeta();

        // 5. 获取最新的01.07版本固件
        String latestVersion = getLatestVersion(availableUpgrades, "01.07");
        String latestSubVersion = latestVersion.substring(6, 11);

        // 6. 检查是否需要指定版本升级（针对Beta用户的特殊升级路径）
        if (upgradeProperties.getFirmwareUpgradeSwitch() == 1 && isBetaUser) {
            String targetVersion = betaUserInfo.getVersion();
            // 6.1 相等不提示升级
            if (formattedVersion.equals(targetVersion)) {
                return noUpgradeResult(null);
            }
            // 6.2 大于目标版本，不提示升级
            if (formattedVersion.compareTo(targetVersion) > 0) {
                return noUpgradeResult(formattedVersion);
            }
            // 6.3 小于目标版本，提示升级
            log.info("beta user {} need upgrade version: {}", userId, targetVersion);
            return createUpgradeResult(
                    betaUserInfo.getType(),
                    targetVersion,
                    findUpgradeEntityByVersion(availableUpgrades, targetVersion)
            );
        }

        // 7. 根据主版本确定升级策略
        if ("01.07".equals(mainVersion)) {
            // 7.1 对于01.07主版本，检查子版本是否需要升级
            if (subVersion.equals(latestSubVersion)) {
                // 已是最新子版本，无需升级
                return noUpgradeResult(formattedVersion);
            } else {
                // 需要升级到最新子版本，提示升级
                log.info("user {} need from {} upgrade to {}", userId, formattedVersion, latestVersion);
                return createUpgradeResult(
                        UpgradeWayConst.UPGRADE_SILENT,
                        latestVersion,
                        findUpgradeEntityByVersion(availableUpgrades, latestVersion)
                );
            }
        } else if ("01.06".equals(mainVersion)) {
            // 7.2 对于01.06主版本，强制升级到01.07
            log.info("user {} need from 01.06 version force upgrde to {}", userId, latestVersion);
            return createUpgradeResult(
                    UpgradeWayConst.UPGRADE_FORCE,
                    latestVersion,
                    findUpgradeEntityByVersion(availableUpgrades, latestVersion)
            );
        } else {
            // 7.3 其他主版本暂不处理
            return noUpgradeResult(formattedVersion);
        }
    }

    /**
     * 格式化版本号为标准格式 (例如: "01070000" -> "01.07.00.00")
     *
     * @param rawVersion 原始版本号
     * @return 格式化后的版本号
     */
    private String formatVersionNumber(String rawVersion) {
        return rawVersion.substring(0, 2)
                + "." + rawVersion.substring(2, 4)
                + "." + rawVersion.substring(4, 6)
                + "." + rawVersion.substring(6, 8);
    }

    /**
     * 获取指定主版本的最新版本号
     *
     * @param upgradeEntities   可用的升级实体列表
     * @param mainVersionPrefix 主版本前缀 (例如: "01.07")
     * @return 最新版本号
     */
    private String getLatestVersion(List<AppFirmwareUpgradeEntity> upgradeEntities, String mainVersionPrefix) {
        List<String> filteredVersions = upgradeEntities.stream()
                .map(AppFirmwareUpgradeEntity::getVersion)
                .filter(version -> version.startsWith(mainVersionPrefix))
                .sorted()
                .collect(Collectors.toList());

        return filteredVersions.get(filteredVersions.size() - 1);
    }

    /**
     * 根据版本号查找对应的升级实体
     *
     * @param upgradeEntities 可用的升级实体列表
     * @param version         目标版本号
     * @return 对应的升级实体
     */
    private AppFirmwareUpgradeEntity findUpgradeEntityByVersion(List<AppFirmwareUpgradeEntity> upgradeEntities, String version) {
        return upgradeEntities.stream()
                .filter(entity -> entity.getVersion().equals(version))
                .findFirst()
                .orElseThrow(() -> new UpgradeException("not found " + version));
    }

    /**
     * 创建不升级结果
     *
     * @param version 当前版本号，可为null
     * @return 静默升级结果
     */
    private FirmwareUpgradeResultVO noUpgradeResult(String version) {
        FirmwareUpgradeResultVO result = new FirmwareUpgradeResultVO();
        result.setUpgradeFlag(UpgradeWayConst.NO_UPGRADE);
        result.setVersion(version);
        result.setUrl(null);
        result.setFirmwareContent(null);
        return result;
    }

    /**
     * 创建升级结果
     *
     * @param upgradeFlag   升级方式
     * @param version       目标版本号
     * @param upgradeEntity 升级实体
     * @return 升级结果
     */
    private FirmwareUpgradeResultVO createUpgradeResult(Integer upgradeFlag, String version, AppFirmwareUpgradeEntity upgradeEntity) {
        FirmwareUpgradeResultVO result = new FirmwareUpgradeResultVO();
        result.setUpgradeFlag(upgradeFlag);
        result.setVersion(version);
        result.setUrl(upgradeEntity.getLink());
        result.setFirmwareContent(upgradeEntity.getFirmwareContent());
        return result;
    }
}
