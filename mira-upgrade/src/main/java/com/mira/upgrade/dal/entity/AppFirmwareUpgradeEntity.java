package com.mira.upgrade.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@TableName("app_firmware_upgrade")
public class AppFirmwareUpgradeEntity {
    @TableId(type = IdType.AUTO)
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 版本号
     */
    private String version;

    /**
     * 下载地址
     */
    private String link;

    /**
     * 分类
     */
    private Integer type;

    /**
     * bata测试标记，1表示bata测试中，默认0
     */
    private Integer bataFlag;

    /**
     * 发布状态
     */
    private Integer state;

    /**
     * 目标平台
     */
    private Integer platform;

    /**
     * 目标用户
     */
    private String target;

    /**
     * 升级提示内容
     */
    private String firmwareContent;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 最后修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
