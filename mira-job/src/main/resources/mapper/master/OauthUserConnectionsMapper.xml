<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.job.dal.mapper.master.OauthUserConnectionsMapper">
    <select id="listByStatus" resultType="com.mira.job.consts.dto.OauthUserConnectionsDTO">
        select a.client_id, u.id as user_id, r.cycle_data, u.time_zone
        from oauth_user_connections a
        join (select id from oauth_user_connections order by id asc limit #{pageSize},#{queryBatch}) b on a.id=b.id
        left join app_user u on a.email = u.email
        left join app_user_algorithm_result r on u.id = r.user_id
        where a.status = #{status}
    </select>
</mapper>
