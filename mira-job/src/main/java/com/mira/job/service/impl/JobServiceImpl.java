package com.mira.job.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.job.consts.JobStatusConsts;
import com.mira.api.job.consts.JobTypeConsts;
import com.mira.api.job.dto.*;
import com.mira.api.message.dto.FirebasePushDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.StringUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.consts.dto.PushUserInfoDTO;
import com.mira.job.consts.dto.desk.XxlJobInfo;
import com.mira.job.dal.dao.master.AdminNotificationJobDAO;
import com.mira.job.dal.dao.master.AppUserDAO;
import com.mira.job.dal.dao.master.AppUserInfoDAO;
import com.mira.job.dal.dao.master.SysNotificationDefineDAO;
import com.mira.job.dal.entity.master.AdminNotificationJobEntity;
import com.mira.job.dal.entity.master.AppUserEntity;
import com.mira.job.dal.entity.master.AppUserInfoEntity;
import com.mira.job.dal.entity.master.SysNotificationDefineEntity;
import com.mira.job.exception.JobException;
import com.mira.job.service.IJobService;
import com.mira.job.service.handler.desk.FrequencyJobHandler;
import com.mira.job.service.manager.DeskPushManager;
import com.mira.job.service.manager.PushUserInfoCacheManager;
import com.mira.job.service.util.DeskJobUtil;
import com.mira.job.service.util.FirebaseBuildUtil;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

/**
 * job service interface implements
 *
 * <AUTHOR>
 */
@Service
public class JobServiceImpl implements IJobService {
    @Value("${xxl.job.admin.addresses}")
    private String jobServer;
    @Value("${xxl.job.accessToken}")
    private String accessToken;
    @Value("${xxljob.group}")
    private int jobGroup;

    @Resource
    private AppUserDAO appUserDAO;
    @Resource
    private AppUserInfoDAO appUserInfoDAO;
    @Resource
    private AdminNotificationJobDAO adminNotificationJobDAO;
    @Resource
    private SysNotificationDefineDAO sysNotificationDefineDAO;
    @Resource
    private DeskPushManager deskPushManager;
    @Resource
    private PushUserInfoCacheManager pushUserInfoCacheManager;

    private ThreadPoolTaskScheduler taskScheduler;

    /**
     * default time zone
     */
    private final static String TIME_ZONE = "America/Los_Angeles";

    /**
     * store scheduled future
     */
    private final ConcurrentHashMap<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        taskScheduler = new ThreadPoolTaskScheduler();
        taskScheduler.setRemoveOnCancelPolicy(true);
        taskScheduler.setWaitForTasksToCompleteOnShutdown(true);
        taskScheduler.setPoolSize(10);
        taskScheduler.setThreadNamePrefix("PromoJobTask-");
        taskScheduler.initialize();
    }

    @EventListener(ApplicationReadyEvent.class)
    public void loadJobData() {
        // 重启时加载启动中的job
        List<AdminNotificationJobEntity> jobEntityList = adminNotificationJobDAO.jobListByStart();
        long now = System.currentTimeMillis();
        for (AdminNotificationJobEntity job : jobEntityList) {
            if (StringUtils.isBlank(job.getStartTime())) {
                continue;
            }
            Long startTimestamp = ZoneDateUtil.timestamp(TIME_ZONE, job.getStartTime(), DatePatternConst.DATE_TIME_PATTERN);
            if (now > startTimestamp) {
                continue;
            }
            Long timestamp = ZoneDateUtil.timestamp(TIME_ZONE, job.getStartTime(), DatePatternConst.DATE_TIME_PATTERN);
            ScheduledFuture<?> scheduledFuture = taskScheduler.schedule(createNewTask(job.getTaskId()), Instant.ofEpochMilli(timestamp));
            scheduledTasks.put(job.getTaskId(), scheduledFuture);
        }
    }

    private PromoJobTask createNewTask(String taskId) {
        return new PromoJobTask(adminNotificationJobDAO, sysNotificationDefineDAO,
                deskPushManager, pushUserInfoCacheManager, scheduledTasks, taskId);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addJob(NotificationPushJobDTO pushJobDTO) {
        Long adminId = pushJobDTO.getAdminId();
        String adminUsername = pushJobDTO.getAdminUsername();
        String author = adminId + " - " + adminUsername;
        NotificationPushCreateDTO pushCreateDTO = pushJobDTO.getPushCreateDTO();

        // task id
        String taskId = StringUtil.random(8);
        // save define
        SysNotificationDefineEntity defineEntity = new SysNotificationDefineEntity();
        Long newDefineId = sysNotificationDefineDAO.getMaxDefineId() + 1;
        buildNotificationDefine(defineEntity, pushCreateDTO, newDefineId);
        UpdateEntityTimeUtil.setBaseEntityTime(TIME_ZONE, defineEntity);
        sysNotificationDefineDAO.save(defineEntity);
        // add job
        checkFrequencyJobTrigger(pushCreateDTO);
        Integer jobType = pushCreateDTO.getTaskType();
        if (jobType == null) {
            jobType = JobTypeConsts.ONE_TIME;
        }
        AdminNotificationJobEntity adminNotificationJobEntity = new AdminNotificationJobEntity();
        adminNotificationJobEntity.setTaskId(taskId);
        adminNotificationJobEntity.setDefineId(defineEntity.getDefineId());
        adminNotificationJobEntity.setAdminId(adminId);
        adminNotificationJobEntity.setAdminName(adminUsername);
        adminNotificationJobEntity.setStartTime(pushCreateDTO.getDateTime());
        adminNotificationJobEntity.setJobJson(JsonUtil.toJson(pushCreateDTO));
        adminNotificationJobEntity.setJobType(jobType);
        UpdateEntityTimeUtil.setBaseEntityTime(TIME_ZONE, adminNotificationJobEntity);
        // frequency job
        if (jobType.equals(JobTypeConsts.FIXED_FREQUENCY)) {
            createFrequencyJob(taskId, author, newDefineId, adminNotificationJobEntity, pushCreateDTO);
        }
        // save job
        adminNotificationJobDAO.save(adminNotificationJobEntity);
    }

    private void checkFrequencyJobTrigger(NotificationPushCreateDTO pushCreateDTO) {
        boolean result = (ArrayUtils.isNotEmpty(pushCreateDTO.getCycle()) && ArrayUtils.isNotEmpty(pushCreateDTO.getDayInCycle()))
                || (ArrayUtils.isEmpty(pushCreateDTO.getCycle()) && ArrayUtils.isEmpty(pushCreateDTO.getDayInCycle()));
        if (!result) {
            throw new JobException("Cycle and Day in cycle must both have values.");
        }
    }

    private void buildNotificationDefine(SysNotificationDefineEntity sysNotificationDefineEntity,
                                         NotificationPushCreateDTO pushCreateDTO,
                                         Long defineId) {
        String expireTime = pushCreateDTO.getExpireTime();
        Long expireTimestamp = -1L;
        if (StringUtils.isNotBlank(expireTime)) {
            expireTimestamp = ZoneDateUtil.timestamp(TIME_ZONE, pushCreateDTO.getExpireTime(), DatePatternConst.DATE_TIME_PATTERN);
        }

        sysNotificationDefineEntity.setDefineId(defineId);
        sysNotificationDefineEntity.setTitle(pushCreateDTO.getTitle());
        sysNotificationDefineEntity.setIcon(pushCreateDTO.getIcon());
        sysNotificationDefineEntity.setType(4);
        sysNotificationDefineEntity.setContent(pushCreateDTO.getContent());
        sysNotificationDefineEntity.setStyleType(pushCreateDTO.getStyleType());
        sysNotificationDefineEntity.setButton1(pushCreateDTO.getButton1());
        sysNotificationDefineEntity.setButton2(pushCreateDTO.getButton2());
        sysNotificationDefineEntity.setButton1Link(pushCreateDTO.getButton1Link());
        sysNotificationDefineEntity.setButton2Link(pushCreateDTO.getButton2Link());
        sysNotificationDefineEntity.setPictureUrl(pushCreateDTO.getPictureUrl());
        sysNotificationDefineEntity.setExpireTime(expireTimestamp);
        sysNotificationDefineEntity.setBackgroundImage(pushCreateDTO.getBackgroundImage());
        sysNotificationDefineEntity.setBackgroundColor(pushCreateDTO.getBackgroundColor());
        sysNotificationDefineEntity.setLanguage("en-us");
    }

    private void createFrequencyJob(String taskId, String author, Long defineId,
                                    AdminNotificationJobEntity adminNotificationJobEntity,
                                    NotificationPushCreateDTO pushCreateDTO) {
        XxlJobInfo xxlJobInfo = buildXxlJobInfo(taskId, author, defineId, pushCreateDTO);
        String jobId = DeskJobUtil.addJob(jobServer, accessToken, xxlJobInfo, taskId);
        adminNotificationJobEntity.setXxljobId(Integer.valueOf(jobId));
    }

    private XxlJobInfo buildXxlJobInfo(String taskId, String author, Long defineId,
                                       NotificationPushCreateDTO pushCreateDTO) {
        XxlJobInfo xxlJobInfo = new XxlJobInfo();
        // build
        xxlJobInfo.setAuthor(author);
        xxlJobInfo.setJobGroup(jobGroup);
        xxlJobInfo.setJobDesc(taskId);
        xxlJobInfo.setScheduleType("CRON");
        xxlJobInfo.setScheduleConf("0 " + RandomUtils.nextInt(0, 60) + " * * * ?");
        xxlJobInfo.setGlueType("GLUE_GROOVY");
        xxlJobInfo.setExecutorRouteStrategy("ROUND");
        xxlJobInfo.setMisfireStrategy("DO_NOTHING");
        xxlJobInfo.setExecutorBlockStrategy("DISCARD_LATER");
        xxlJobInfo.setExecutorTimeout(0);
        xxlJobInfo.setExecutorFailRetryCount(0);
        xxlJobInfo.setGlueRemark("Backend Task");
        xxlJobInfo.setGlueSource(FrequencyJobHandler.getHandlerSource());
        // set param
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("taskId", taskId);
        paramMap.put("defineId", String.valueOf(defineId));
        paramMap.put("cycle", JsonUtil.toJson(pushCreateDTO.getCycle()));
        paramMap.put("dayInCycle", JsonUtil.toJson(pushCreateDTO.getDayInCycle()));
        xxlJobInfo.setExecutorParam(JsonUtil.toJson(paramMap));
        return xxlJobInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateJob(NotificationPushJobDTO pushJobDTO) {
        String taskId = pushJobDTO.getTaskId();
        Long adminId = pushJobDTO.getAdminId();
        String adminUsername = pushJobDTO.getAdminUsername();
        NotificationPushCreateDTO pushCreateDTO = pushJobDTO.getPushCreateDTO();

        AdminNotificationJobEntity jobEntity = adminNotificationJobDAO.getByTaskId(taskId);
        if (jobEntity == null) {
            throw new JobException("The push task does not exist.");
        }
        Integer jobStatus = jobEntity.getJobStatus();
        if (JobStatusConsts.STARTING.equals(jobStatus) || JobStatusConsts.RUNNING.equals(jobStatus)) {
            throw new JobException("The starting or running status cannot be edited.");
        }
        Integer jobType = jobEntity.getJobType();

        // check task type
        Integer taskType = pushCreateDTO.getTaskType();
        if (!jobType.equals(taskType)) {
            throw new JobException("The push task type cannot be changed.");
        }

        // update define
        SysNotificationDefineEntity defineEntity = sysNotificationDefineDAO.getByDefineId(jobEntity.getDefineId(), "en-us");
        buildNotificationDefine(defineEntity, pushCreateDTO, defineEntity.getDefineId());
        UpdateEntityTimeUtil.updateBaseEntityTime(TIME_ZONE, defineEntity);
        sysNotificationDefineDAO.updateById(defineEntity);
        // update job
        jobEntity.setAdminId(adminId);
        jobEntity.setAdminName(adminUsername);
        jobEntity.setStartTime(pushCreateDTO.getDateTime());
        jobEntity.setJobJson(JsonUtil.toJson(pushCreateDTO));
        UpdateEntityTimeUtil.setBaseEntityTime(TIME_ZONE, jobEntity);
        adminNotificationJobDAO.updateById(jobEntity);
        // update xxljob
        if (Objects.equals(jobType, JobTypeConsts.FIXED_FREQUENCY)) {
            String author = adminId + " - " + adminUsername;
            XxlJobInfo xxlJobInfo = buildXxlJobInfo(taskId, author, jobEntity.getDefineId(), pushCreateDTO);
            xxlJobInfo.setId(jobEntity.getXxljobId());
            DeskJobUtil.updateJob(jobServer, accessToken, xxlJobInfo, taskId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void startJob(NotificationPushJobDTO pushJobDTO) {
        String taskId = pushJobDTO.getTaskId();

        AdminNotificationJobEntity jobEntity = adminNotificationJobDAO.getByTaskId(taskId);
        if (jobEntity == null) {
            throw new JobException("The push task does not exist.");
        }
        String nowTime = ZoneDateUtil.format(TIME_ZONE, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
        NotificationPushCreateDTO pushCreateDTO = JsonUtil.toObject(jobEntity.getJobJson(), NotificationPushCreateDTO.class);
        if (StringUtils.isNotBlank(pushCreateDTO.getExpireTime())
                && LocalDateUtil.after(nowTime, pushCreateDTO.getExpireTime(), DatePatternConst.DATE_TIME_PATTERN)) {
            throw new JobException("It has passed the expire time, please reset the expire time.");
        }

        Integer jobType = jobEntity.getJobType();
        if (Objects.equals(jobType, JobTypeConsts.ONE_TIME)) {
            // check
            if (LocalDateUtil.after(nowTime, jobEntity.getStartTime(), DatePatternConst.DATE_TIME_PATTERN)) {
                throw new JobException("It has passed the push time, please reset the push time.");
            }

            // job start
            Long timestamp = ZoneDateUtil.timestamp(TIME_ZONE, jobEntity.getStartTime(), DatePatternConst.DATE_TIME_PATTERN);
            ScheduledFuture<?> scheduledFuture = taskScheduler.schedule(createNewTask(taskId), Instant.ofEpochMilli(timestamp));
            // update/save to map
            scheduledTasks.putIfAbsent(taskId, scheduledFuture);
        } else if (Objects.equals(jobType, JobTypeConsts.FIXED_FREQUENCY)) {
            DeskJobUtil.startJob(jobServer, accessToken, jobEntity.getXxljobId(), taskId);
        }

        // update db
        jobEntity.setJobStatus(JobStatusConsts.STARTING);
        UpdateEntityTimeUtil.updateBaseEntityTime(TIME_ZONE, jobEntity);
        adminNotificationJobDAO.updateById(jobEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void stopJob(NotificationPushJobDTO pushJobDTO) {
        String taskId = pushJobDTO.getTaskId();

        AdminNotificationJobEntity jobEntity = adminNotificationJobDAO.getByTaskId(taskId);
        if (jobEntity == null) {
            throw new JobException("The push task does not exist.");
        }
        Integer jobType = jobEntity.getJobType();

        if (Objects.equals(jobType, JobTypeConsts.ONE_TIME)) {
            // remove to the map and cancel job
            ScheduledFuture<?> scheduledFuture = scheduledTasks.get(taskId);
            if (scheduledFuture != null) {
                scheduledFuture.cancel(true);
            }
            scheduledTasks.remove(taskId);
        } else if (Objects.equals(jobType, JobTypeConsts.FIXED_FREQUENCY)) {
            DeskJobUtil.stopJob(jobServer, accessToken, jobEntity.getXxljobId(), taskId);
        }

        // update db
        jobEntity.setJobStatus(JobStatusConsts.NOT_START);
        UpdateEntityTimeUtil.updateBaseEntityTime(TIME_ZONE, jobEntity);
        adminNotificationJobDAO.updateById(jobEntity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void removeJob(NotificationPushJobDTO pushJobDTO) {
        String taskId = pushJobDTO.getTaskId();

        AdminNotificationJobEntity jobEntity = adminNotificationJobDAO.getByTaskId(taskId);
        if (jobEntity == null) {
            throw new JobException("The push task does not exist.");
        }
        Integer jobType = jobEntity.getJobType();

        if (Objects.equals(jobType, JobTypeConsts.ONE_TIME)) {
            // remove to the map and cancel job
            ScheduledFuture<?> scheduledFuture = scheduledTasks.get(taskId);
            if (scheduledFuture != null) {
                scheduledFuture.cancel(true);
            }
            scheduledTasks.remove(taskId);
        } else if (Objects.equals(jobType, JobTypeConsts.FIXED_FREQUENCY)) {
            DeskJobUtil.removeJob(jobServer, accessToken, jobEntity.getXxljobId(), taskId);
        }

        // delete db
        jobEntity.setJobStatus(JobStatusConsts.NOT_START);
        UpdateEntityTimeUtil.updateBaseEntityTime(TIME_ZONE, jobEntity);
        adminNotificationJobDAO.removeById(jobEntity);
    }

    @Override
    public NotificationJobPageDTO listJob(long current, long size) {
        long currIndex = (current - 1) * size;
        List<AdminNotificationJobEntity> jobEntityList = adminNotificationJobDAO.jobList(currIndex, size);

        List<NotificationJobListDTO> jobList = new ArrayList<>();
        for (AdminNotificationJobEntity jobEntity : jobEntityList) {
            NotificationPushCreateDTO pushCreateDTO = JsonUtil.toObject(jobEntity.getJobJson(), NotificationPushCreateDTO.class);
            Integer jobStatus = jobEntity.getJobStatus();
            Integer jobType = jobEntity.getJobType();
            // 判断是否正在运行
            if (Objects.equals(jobType, JobTypeConsts.ONE_TIME)) {
                ScheduledFuture<?> scheduledFuture = scheduledTasks.get(jobEntity.getTaskId());
                long startTimestamp = ZoneDateUtil.timestamp(TIME_ZONE, jobEntity.getStartTime(), DatePatternConst.DATE_TIME_PATTERN);
                long now = System.currentTimeMillis();
                if (now >= startTimestamp && scheduledFuture != null && !scheduledFuture.isDone()) {
                    jobStatus = JobStatusConsts.RUNNING;
                }
            } else if (Objects.equals(jobType, JobTypeConsts.FIXED_FREQUENCY)) {
                jobStatus = Objects.equals(jobEntity.getJobStatus(), JobStatusConsts.STARTING)
                        ? JobStatusConsts.RUNNING : JobStatusConsts.NOT_START;
            }

            // vo
            NotificationJobListDTO pushCreateVO = BeanUtil.toBean(pushCreateDTO, NotificationJobListDTO.class);
            pushCreateVO.setTaskId(jobEntity.getTaskId());
            pushCreateVO.setTaskStatus(jobStatus);
            jobList.add(pushCreateVO);
        }

        NotificationJobPageDTO notificationJobPageDTO = new NotificationJobPageDTO();
        notificationJobPageDTO.setRecordCount(adminNotificationJobDAO.count());
        notificationJobPageDTO.setJobListDTOS(jobList);

        return notificationJobPageDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void preview(NotificationPushPreviewDTO pushPreviewDTO) {
        AdminNotificationJobEntity jobEntity = adminNotificationJobDAO.getByTaskId(pushPreviewDTO.getTaskId());
        if (jobEntity == null) {
            throw new JobException("The push task does not exist.");
        }
        SysNotificationDefineEntity notificaitonDefine = sysNotificationDefineDAO.getByDefineId(jobEntity.getDefineId(), "en-us");
        if (notificaitonDefine == null) {
            throw new JobException("The define info does not exist.");
        }
        AppUserEntity appUser = appUserDAO.getByEmail(pushPreviewDTO.getEmail());
        if (appUser == null) {
            throw new JobException("The user does not exist.");
        }
        AppUserInfoEntity appUserInfo = appUserInfoDAO.getByUserId(appUser.getId());
        if (appUserInfo == null) {
            throw new JobException("The user info does not exist.");
        }

        // push
        PushUserInfoDTO pushUserInfoDTO = BeanUtil.toBean(appUserInfo, PushUserInfoDTO.class);
        List<PushUserInfoDTO> pushUserInfoList = new ArrayList<>();
        pushUserInfoList.add(pushUserInfoDTO);
        FirebasePushDTO firebasePushDTO = FirebaseBuildUtil.buildPushNotification(notificaitonDefine, false);
        Map<FirebasePushDTO, List<PushUserInfoDTO>> firebasePushMap = new HashMap<>();
        firebasePushMap.put(firebasePushDTO, pushUserInfoList);
        deskPushManager.firebasePush(firebasePushMap, null);
    }
}
