package com.mira.job.schedule.testing;

import com.mira.api.user.dto.user.UserReminderInfoDTO;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.util.LocalDateUtil;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.core.util.ZoneDateUtil;
import com.mira.job.dal.dao.master.AppPregnantModeInfoV2DAO;
import com.mira.job.dal.dao.master.AppUserReminderDAO;
import com.mira.job.dal.entity.master.AppPregnantModeInfoV2Entity;
import com.mira.job.service.manager.JobManager;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;

/**
 * update testing reminder schedule
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class UpdateTestingReminderSchedule {
    @Resource
    private AppPregnantModeInfoV2DAO appPregnantModeInfoV2DAO;
    @Resource
    private AppUserReminderDAO appUserReminderDAO;
    @Resource
    private JobManager jobManager;

    @XxlJob("updateTestingReminderHandler")
    public void updateTestingReminderHandler() {
        log.info("updateTestingReminderHandler: start execute");
        run();
        log.info("updateTestingReminderHandler: end execute");
        log.info("------------------------------------");
    }

    private void run() {
        int queryBatch = 10000;
        long pageSize = 0;
        long recordCount = appPregnantModeInfoV2DAO.getCount();
        log.info("app_pregnant_mode_info_v2总数：{}", recordCount);
        // 分批查询
        long cdCount = recordCount % queryBatch == 0 ? recordCount / queryBatch : recordCount / queryBatch + 1;
        CountDownLatch cd = new CountDownLatch((int) cdCount);
        while (recordCount > 0) {
            String sql = "select a.user_id,a.is_end,a.end_reason,a.modify_time,a.modify_time_str from app_pregnant_mode_info_v2 a " +
                    "join (select id from app_pregnant_mode_info_v2 where deleted=0 order by id asc limit " + pageSize + "," + queryBatch + ") b " +
                    "on a.id=b.id";
            CompletableFuture.runAsync(() -> {
                List<AppPregnantModeInfoV2Entity> pregnantModeInfoV2List = appPregnantModeInfoV2DAO.getBaseMapper().listBySql(sql);
                if (CollectionUtils.isNotEmpty(pregnantModeInfoV2List)) {
                    handle(pregnantModeInfoV2List);
                }
                cd.countDown();
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                cd.countDown();
                log.error("getAllPregnantModeInfoV2 error occurred, sql:{}", sql, ex);
                return null;
            });

            pageSize += queryBatch;
            recordCount -= queryBatch;
        }

        // wait future
        try {
            cd.await();
        } catch (InterruptedException ex) {
            log.error(ex.getMessage(), ex);
        }
    }

    private void handle(List<AppPregnantModeInfoV2Entity> pregnantModeInfoV2List) {
        // 每个用户取modify_time最新的一条
        Map<Long, AppPregnantModeInfoV2Entity> pregnantModeInfoMap = new HashMap<>();
        for (AppPregnantModeInfoV2Entity entity : pregnantModeInfoV2List) {
            Long userId = entity.getUserId();
            AppPregnantModeInfoV2Entity currentLatest = pregnantModeInfoMap.get(userId);
            if (currentLatest == null || entity.getModifyTime() > currentLatest.getModifyTime()) {
                pregnantModeInfoMap.put(userId, entity);
            }
        }
        // 遍历
        for (Map.Entry<Long, AppPregnantModeInfoV2Entity> entry : pregnantModeInfoMap.entrySet()) {
            Long userId = entry.getKey();
            AppPregnantModeInfoV2Entity currentLatest = pregnantModeInfoMap.get(userId);
            // 已结束并且为miscarriage
            if (currentLatest != null
                    && (currentLatest.getIsEnd() == 1 && currentLatest.getEndReason() == 2)) {
                // 当前test_schedule_flag值
                UserReminderInfoDTO userReminderInfo = jobManager.getUserReminderInfo(userId);
                if (userReminderInfo == null) {
                    continue;
                }
                Integer testingScheduleFlag = userReminderInfo.getTestingScheduleFlag();
                // 记录miscarriage后的30天内不提醒测试
                String userCurrentDay = ZoneDateUtil
                        .format(currentLatest.getTimeZone(), System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
                int days = LocalDateUtil.minusToDay(userCurrentDay, currentLatest.getModifyTimeStr());
                if (days > 30) {
                    // 如果已经关闭，那么打开
                    if (testingScheduleFlag == 0) {
                        jobManager.updateTestScheduleFlag(userId, 1);
                        appUserReminderDAO.updateTestingScheduleFlag(userId, 1);
                    }
                    continue;
                }
                // 关闭test_schedule_flag
                if (testingScheduleFlag == 1) {
                    jobManager.updateTestScheduleFlag(userId, 0);
                    appUserReminderDAO.updateTestingScheduleFlag(userId, 0);
                }
            }
        }
    }
}
