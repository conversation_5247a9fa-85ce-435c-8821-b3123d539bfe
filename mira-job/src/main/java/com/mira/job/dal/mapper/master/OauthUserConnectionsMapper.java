package com.mira.job.dal.mapper.master;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.job.consts.dto.OauthUserConnectionsDTO;
import com.mira.job.dal.entity.master.OauthUserConnectionsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OauthUserConnectionsMapper extends BaseMapper<OauthUserConnectionsEntity> {
    List<OauthUserConnectionsDTO> listByStatus(@Param("status") String status,
                                               @Param("pageSize") long pageSize,
                                               @Param("queryBatch") long queryBatch);
}
