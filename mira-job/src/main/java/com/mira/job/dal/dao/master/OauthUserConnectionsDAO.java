package com.mira.job.dal.dao.master;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.job.consts.dto.OauthUserConnectionsDTO;
import com.mira.job.dal.entity.master.OauthUserConnectionsEntity;
import com.mira.job.dal.mapper.master.OauthUserConnectionsMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class OauthUserConnectionsDAO extends ServiceImpl<OauthUserConnectionsMapper, OauthUserConnectionsEntity> {
    public List<OauthUserConnectionsDTO> listByStatus(String status, long pageSize, long queryBatch) {
        return baseMapper.listByStatus(status, pageSize, queryBatch);
    }

    public long getCount() {
        return count();
    }
}
