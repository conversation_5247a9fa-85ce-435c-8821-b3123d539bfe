package com.mira.clinic.factory.email;

import com.mira.clinic.dal.entity.AppTenantDoctorEntity;
import com.mira.clinic.dal.entity.AppTenantEntity;
import com.mira.core.util.JsonUtil;
import com.mira.core.util.RsaUtil;
import com.mira.web.properties.RsaProperties;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 邀请Doctor邮件模版工厂
 *
 * <AUTHOR>
 */
@SuppressWarnings("all")
public class InviteDoctorEmailFactory implements EmailTemplateFactory<AppTenantDoctorEntity> {
    private final RsaProperties rsaProperties;
    private final AppTenantEntity appTenant;
    private final int sendFlag;

    public InviteDoctorEmailFactory(RsaProperties rsaProperties, AppTenantEntity appTenant, int sendFlag) {
        this.rsaProperties = rsaProperties;
        this.appTenant = appTenant;
        this.sendFlag = sendFlag;
    }

    @Override
    public Map<String, String> createTemplate(AppTenantDoctorEntity appTenantDoctor) {
        // 邮件内链接加密数据
        Map<String, Object> encryptMap = new HashMap<>();
        encryptMap.put("doctorId", appTenantDoctor.getId());
        encryptMap.put("doctorInviteTimestamp", System.currentTimeMillis());
        encryptMap.put("sendFlag", sendFlag);
        // 邮件内变量
        String encryptStr = RsaUtil.encryptionRsa(JsonUtil.toJson(encryptMap), rsaProperties.getPublicKey());
        HashMap<String, String> variableMap = new HashMap<>();
        variableMap.put("inviteName", appTenantDoctor.getName());
        variableMap.put("tenantName", appTenant.getName());
        variableMap.put("hash", URLEncoder.encode(encryptStr, StandardCharsets.UTF_8));
        return variableMap;
    }

    public static Map<String, String> createTemplate(AppTenantDoctorEntity appTenantDoctor,
                                                     AppTenantEntity appTenant, int sendFlag,
                                                     RsaProperties rsaProperties) {
        return new InviteDoctorEmailFactory(rsaProperties, appTenant, sendFlag).createTemplate(appTenantDoctor);
    }
}
