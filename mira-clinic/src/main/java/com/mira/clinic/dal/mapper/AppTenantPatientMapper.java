package com.mira.clinic.dal.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mira.clinic.dto.AppTenantPatientDTO;
import com.mira.api.clinic.dto.ClinicPatientListDTO;
import com.mira.clinic.dal.entity.AppTenantPatientEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppTenantPatientMapper extends BaseMapper<AppTenantPatientEntity> {
    /**
     * 管理员视角的所有病人列表
     */
    List<ClinicPatientListDTO> listTenantPatientByClinic(@Param("tenantCode") String tenantCode);

    /**
     * 管理员视角的所有病人分页列表
     */
    List<ClinicPatientListDTO> pageTenantPatientByClinic(@Param("tenantCode") String tenantCode,
                                                         @Param("currIndex") int currIndex,
                                                         @Param("pageSize") int pageSize,
                                                         @Param("keyWord") String keyWord);

    /**
     * 诊所管理员的所有病人数
     */
    Integer countTenantPatientByClinic(@Param("tenantCode") String tenantCode,
                                       @Param("keyWord") String keyWord);

    /**
     * 医生视角的所有病人列表
     */
    List<ClinicPatientListDTO> pageTenantPatientByDoctor(@Param("tenantCode") String tenantCode,
                                                         @Param("doctorId") Long doctorId,
                                                         @Param("currIndex") int currIndex,
                                                         @Param("pageSize") int pageSize,
                                                         @Param("keyWord") String keyWord);

    /**
     * 多个医生的所有病人列表
     */
    List<ClinicPatientListDTO> pageTenantPatientByDoctors(@Param("tenantCode") String tenantCode,
                                                          @Param("doctorIds") List<Long> doctorIds,
                                                          @Param("currIndex") int currIndex,
                                                          @Param("pageSize") int pageSize,
                                                          @Param("keyWord") String keyWord);

    /**
     * 病人信息，以及绑定诊所信息
     */
    List<AppTenantPatientDTO> listByPatientEmail(@Param("email") String email);

    /**
     * 多个医生的所有病人数
     */
    Integer countTenantPatientByDoctors(@Param("tenantCode") String tenantCode,
                                        @Param("doctorIds") List<Long> doctorIds,
                                        @Param("keyWord") String keyWord);

    /**
     * 移除病人
     */
    void removePatient(Long patientId);
}
