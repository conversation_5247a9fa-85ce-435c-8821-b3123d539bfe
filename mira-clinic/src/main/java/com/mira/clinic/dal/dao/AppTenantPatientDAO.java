package com.mira.clinic.dal.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.clinic.dal.entity.AppTenantPatientEntity;
import com.mira.clinic.dal.mapper.AppTenantPatientMapper;
import com.mira.clinic.dto.AppTenantPatientDTO;
import com.mira.api.clinic.dto.ClinicPatientListDTO;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AppTenantPatientDAO extends ServiceImpl<AppTenantPatientMapper, AppTenantPatientEntity> {
    //todo

    /**
     * @param userId
     * @return
     * @deprecated since 7.6.27 replaced by listByUserId
     */
    public AppTenantPatientEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppTenantPatientEntity>lambdaQuery()
                              .eq(AppTenantPatientEntity::getUserId, userId)
                              .eq(AppTenantPatientEntity::getStatus, 2));
    }

    public AppTenantPatientEntity getByUserIdAndTenantCode(Long userId, String tenantCode) {
        return getOne(Wrappers.<AppTenantPatientEntity>lambdaQuery()
                              .eq(AppTenantPatientEntity::getUserId, userId)
                              .eq(AppTenantPatientEntity::getTenantCode, tenantCode)
                              .eq(AppTenantPatientEntity::getStatus, 2));
    }

    public AppTenantPatientEntity getByUserIdAndTenantCodeIgnoreStatus(Long userId, String tenantCode) {
        return getOne(Wrappers.<AppTenantPatientEntity>lambdaQuery()
                              .eq(AppTenantPatientEntity::getUserId, userId)
                              .eq(AppTenantPatientEntity::getTenantCode, tenantCode));
    }

    public List<AppTenantPatientEntity> listByUserId(Long userId) {
        return list(Wrappers.<AppTenantPatientEntity>lambdaQuery()
                            .eq(AppTenantPatientEntity::getUserId, userId)
                            .eq(AppTenantPatientEntity::getStatus, 2));
    }


    /**
     * @param email
     * @return
     * @deprecated since 7.6.27 replaced by listByEmail
     */
    public AppTenantPatientEntity getByEmail(String email) {
        return getOne(Wrappers.<AppTenantPatientEntity>lambdaQuery()
                              .eq(AppTenantPatientEntity::getInitEmail, email));
    }

    public AppTenantPatientEntity getByEmailAndTenantCode(String email, String tenantCode) {
        return getOne(Wrappers.<AppTenantPatientEntity>lambdaQuery()
                              .eq(AppTenantPatientEntity::getInitEmail, email)
                              .eq(AppTenantPatientEntity::getTenantCode, tenantCode)
        );
    }

    public List<AppTenantPatientDTO> listByPatientEmail(String email) {
        return baseMapper.listByPatientEmail(email);
    }

    public AppTenantPatientEntity getByEmailAndStatus(String email, Integer status) {
        return getOne(Wrappers.<AppTenantPatientEntity>lambdaQuery()
                              .eq(AppTenantPatientEntity::getInitEmail, email)
                              .eq(AppTenantPatientEntity::getStatus, status));
    }

    public List<ClinicPatientListDTO> listTenantPatientByClinic(String tenantCode) {
        return baseMapper.listTenantPatientByClinic(tenantCode);
    }

    public Long countTenantPatientByTenantCode(String tenantCode) {
        return count(Wrappers.<AppTenantPatientEntity>lambdaQuery()
                .eq(AppTenantPatientEntity::getTenantCode, tenantCode));
    }

    public List<ClinicPatientListDTO> pageTenantPatientByClinic(String tenantCode,
                                                                int currIndex,
                                                                int pageSize,
                                                                String keyWord) {
        return baseMapper.pageTenantPatientByClinic(tenantCode, currIndex, pageSize, keyWord);
    }

    public Integer countTenantPatientByClinic(String tenantCode,
                                              String keyWord) {
        return baseMapper.countTenantPatientByClinic(tenantCode, keyWord);
    }

    public List<ClinicPatientListDTO> pageTenantPatientByDoctor(String tenantCode,
                                                                Long doctorId,
                                                                int currIndex,
                                                                int pageSize,
                                                                String keyWord) {
        return baseMapper.pageTenantPatientByDoctor(tenantCode, doctorId, currIndex, pageSize, keyWord);
    }

    public List<ClinicPatientListDTO> pageTenantPatientByDoctors(String tenantCode,
                                                                 List<Long> doctorIds,
                                                                 int currIndex,
                                                                 int pageSize,
                                                                 String keyWord) {
        return baseMapper.pageTenantPatientByDoctors(tenantCode, doctorIds, currIndex, pageSize, keyWord);
    }

    public Integer countTenantPatientByDoctors(String tenantCode,
                                               List<Long> doctorIds,
                                               String keyWord) {
        return baseMapper.countTenantPatientByDoctors(tenantCode, doctorIds, keyWord);
    }

    public void removePatient(Long patientId) {
        baseMapper.removePatient(patientId);
    }

}
