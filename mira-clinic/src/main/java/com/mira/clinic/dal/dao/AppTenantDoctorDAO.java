package com.mira.clinic.dal.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.api.clinic.enums.ClinicianStatusEnum;
import com.mira.clinic.dal.entity.AppTenantDoctorEntity;
import com.mira.clinic.dal.mapper.AppTenantDoctorMapper;
import com.mira.core.request.PageDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AppTenantDoctorDAO extends ServiceImpl<AppTenantDoctorMapper, AppTenantDoctorEntity> {
    public AppTenantDoctorEntity getByEmail(String email) {
        return getOne(Wrappers.<AppTenantDoctorEntity>lambdaQuery()
                              .eq(AppTenantDoctorEntity::getEmail, email));
    }

    public Page<AppTenantDoctorEntity> pageByTenantCodeAndRole(PageDTO pageDTO, String tenantCode, Integer role, String keyword) {
        return page(new Page<>(pageDTO.getCurrent(), pageDTO.getSize()),
                Wrappers.<AppTenantDoctorEntity>lambdaQuery()
                        .eq(AppTenantDoctorEntity::getTenantCode, tenantCode)
                        .eq(AppTenantDoctorEntity::getRole, role)
                        .and(wrapper -> wrapper.like(AppTenantDoctorEntity::getEmail, keyword)
                                               .or().like(AppTenantDoctorEntity::getName, keyword))
                        .orderByDesc(AppTenantDoctorEntity::getCreateTime));
    }

    public List<AppTenantDoctorEntity> listByTenantCodeAndRole(String tenantCode, Integer role, String keyword) {
        LambdaQueryWrapper<AppTenantDoctorEntity> queryWrapper =
                Wrappers.<AppTenantDoctorEntity>lambdaQuery()
                        .eq(AppTenantDoctorEntity::getTenantCode, tenantCode)
                        .eq(AppTenantDoctorEntity::getRole, role)
                        .eq(AppTenantDoctorEntity::getStatus, ClinicianStatusEnum.CONFIRMED.getCode());

        if (StringUtils.isBlank(keyword)) {
            queryWrapper.orderByAsc(AppTenantDoctorEntity::getName);
            return list(queryWrapper);
        }

        queryWrapper.and(wrapper -> wrapper.like(AppTenantDoctorEntity::getEmail, keyword)
                                           .or().like(AppTenantDoctorEntity::getName, keyword))
                    .orderByAsc(AppTenantDoctorEntity::getName);
        return list(queryWrapper);
    }

    public List<AppTenantDoctorEntity> listByTenantCode(String tenantCode) {
        LambdaQueryWrapper<AppTenantDoctorEntity> queryWrapper =
                Wrappers.<AppTenantDoctorEntity>lambdaQuery()
                        .eq(AppTenantDoctorEntity::getTenantCode, tenantCode);
        return list(queryWrapper);
    }
}
