package com.mira.clinic.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2022-04-19
 **/
@Getter
@Setter
@TableName("app_tenant_patient")
public class AppTenantPatientEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户code
     */
    private String tenantCode;

    /**
     * 病人编号
     */
    private String patientNumber;

    /**
     * 初始邮箱
     */
    private String initEmail;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 英文名
     */
    private String firstName;

    /**
     * 英文姓
     */
    private String lastName;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 病人账号状态:1:邀请中;2:正常激活状态;0:关闭
     */
    private Integer status;

    /**
     * 记录账户的邀请时间，仅对被邀请中和邀请过期，该字段有意义
     */
    private Long inviteTime;
}

