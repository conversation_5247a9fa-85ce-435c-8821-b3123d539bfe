package com.mira.clinic.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mira.api.clinic.enums.ClinicianStatusEnum;
import com.mira.clinic.enums.ClinicDoctorTypeEnum;
import com.mira.clinic.enums.ClinicRoleEnum;
import com.mira.mybatis.dal.BaseEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("app_tenant_doctor")
public class AppTenantDoctorEntity extends BaseEntity {
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户code
     */
    private String tenantCode;

    /**
     * 管理员名称
     */
    private String name;

    /**
     * 管理员email
     */
    private String email;

    /**
     * 管理员mobile
     */
    private String mobile;

    /**
     * @see ClinicianStatusEnum
     */
    private Integer status;

    /**
     * 管理员角色编码:1:clinic管理员;2:医生
     * @see ClinicRoleEnum
     */
    private Integer role;

    /**
     * 类型：0:clinic 管理员或者 单独的doctor ;1:clinic所属的医生; 默认0
     * @see ClinicDoctorTypeEnum
     */
    private Integer doctorType;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐
     */
    private String salt;

    /**
     * 记录账户的邀请时间，仅对被邀请中和邀请过期，该字段有意义
     */
    private Long inviteTime;
}
