package com.mira.clinic.enums;

import lombok.Getter;

/**
 * 诊所用户角色枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ClinicDoctorTypeEnum {
    ADMIN(0, "clinic 管理员或者 单独的doctor"),
    CLINICIAN(1, "clinic的clinician(医生或者护士)");

    private final Integer code;
    private final String desc;

    ClinicDoctorTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ClinicDoctorTypeEnum getByCode(Integer code) {
        for (ClinicDoctorTypeEnum clinicRoleEnum : ClinicDoctorTypeEnum.values()) {
            if (clinicRoleEnum.getCode().equals(code)) {
                return clinicRoleEnum;
            }
        }
        return null;
    }
}
