package com.mira.clinic.controller.dto;

import com.mira.api.clinic.dto.ClinicPatientPageRequestDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("病人分页参数")
public class ClinicDoctorPatientPageRequestDTO extends ClinicPatientPageRequestDTO {
    @ApiModelProperty(value = "医生id", required = true)
    private Long doctorId;
}
