package com.mira.clinic.controller;

import com.mira.api.user.dto.user.EditPasswordDTO;
import com.mira.clinic.controller.dto.ClinicConfirmRegisterDTO;
import com.mira.clinic.controller.dto.LoginDTO;
import com.mira.clinic.controller.dto.ResetPasswordDTO;
import com.mira.clinic.controller.vo.LoginVO;
import com.mira.clinic.controller.vo.TenantInfoVO;
import com.mira.clinic.controller.vo.VerifyCodeVO;
import com.mira.clinic.service.ISignService;
import com.mira.core.annotation.Anonymous;
import com.mira.core.annotation.IgnoreLog;
import com.mira.core.consts.RequestSendFlag;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;

/**
 * 登录控制器
 *
 * <AUTHOR>
 */
@Api(tags = "登录相关")
@RestController
@RequestMapping("/tenant/sign")
public class SignController {
    @Resource
    private ISignService signService;

    @Anonymous
    @ApiOperation("获取图片验证码")
    @GetMapping("/verify-code")
    public VerifyCodeVO verfiyCode() throws IOException {
        return signService.getVerifyCode();
    }

    @Anonymous
    @ApiOperation("登陆")
    @PostMapping("/login")
    public LoginVO login(@Valid @RequestBody LoginDTO loginDTO) throws Exception {
        return signService.login(loginDTO);
    }

    @Anonymous
    @ApiOperation("退出登陆")
    @PostMapping("/logout")
    public void logout() {
        signService.logout();
    }

    @ApiOperation("获取Doctor用户详情")
    @GetMapping("/info")
    public TenantInfoVO info() {
        return signService.info();
    }

    @ApiOperation("创建CheckToken")
    @PostMapping("/tenant-check-token")
    public String createTenantCheckToken(@RequestParam Long patientId) throws Exception {
        return signService.createTenantCheckToken(patientId);
    }

    @ApiOperation("修改clinic用户的密码")
    @PostMapping("/edit/pw")
    public void editPassword(@Valid @RequestBody EditPasswordDTO editPasswordDTO) {
        signService.editPassword(editPasswordDTO);
    }

    @ApiOperation("同意HIPAA隐私协议")
    @PostMapping("/agree/hipaa")
    public Integer agreeHipaa(@RequestParam Integer agree) {
        return signService.agreeHipaa(agree);
    }

    @Anonymous
    @ApiOperation("忘记密码")
    @PostMapping("/reset-pw")
    public void resetPassword(@RequestParam String email) {
        signService.resetPassword(email, RequestSendFlag.FIRST_SEND);
    }

    @Anonymous
    @ApiOperation("忘记密码再次发送")
    @PostMapping("/reset-pw/resend")
    public void resetPasswordResend(@RequestParam String email) {
        signService.resetPassword(email, RequestSendFlag.RESEND);
    }

    @Anonymous
    @ApiOperation("确认忘记密码")
    @PostMapping("/reset-pw/confirm")
    public String resetPasswordConfim(@Valid @RequestBody ResetPasswordDTO resetPasswordDTO) {
        return signService.resetPasswordConfirm(resetPasswordDTO);
    }

    @Anonymous
    @IgnoreLog
    @ApiOperation("获取忘记密码的状态")
    @PostMapping("/reset-pw-status")
    public String resetPasswordStatus(@RequestParam String email) {
        return signService.resetPasswordStatus(email);
    }

    /**
     * 从mira-desk发起的邀请诊所
     * @param clinicConfirmRegisterDTO
     * @return
     */
    @Anonymous
    @ApiOperation("确认邀请注册")
    @PostMapping("/invite/activation")
    public String confirmRegister(@RequestBody ClinicConfirmRegisterDTO clinicConfirmRegisterDTO) {
        return signService.confirmRegister(clinicConfirmRegisterDTO);
    }
}
