package com.mira.clinic.controller.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("创建病人请求参数")
public class InvitePatientDTO {
    /**
     * If left blank, auto assign an ID
     */
    @ApiModelProperty(value = "病人编号", required = false)
    private String patientNumber;

    @ApiModelProperty(value = "英文名", required = true)
    private String firstName;

    @ApiModelProperty(value = "英文姓", required = true)
    private String lastName;

    @ApiModelProperty(value = "病人email", required = true)
    private String email;

    @ApiModelProperty("doctor列表")
    private List<Long> doctorIds;
}
