package com.mira.clinic.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("病人列表")
public class ClinicPatientPageVO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("病人状态，参考PatientStatusEnum")
    private Integer patientStatus;

    @ApiModelProperty("病人修改时间")
    private Long modifyTime;

    @ApiModelProperty("病人email")
    private String email;

    @ApiModelProperty("病人mobile")
    private String mobile;

    @ApiModelProperty("病人名称")
    private String nickName;

    @ApiModelProperty("病人编号")
    private String patientNumber;
}
