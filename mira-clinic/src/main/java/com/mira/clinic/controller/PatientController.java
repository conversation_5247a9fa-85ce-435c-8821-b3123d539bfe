package com.mira.clinic.controller;

import com.mira.clinic.controller.dto.EditPatientDoctorDTO;
import com.mira.clinic.controller.dto.PatientDataPageDTO;
import com.mira.clinic.controller.dto.ClinicDoctorPatientPageRequestDTO;
import com.mira.api.clinic.dto.ClinicPatientPageRequestDTO;
import com.mira.clinic.controller.vo.ClinicPatientPageVO;
import com.mira.clinic.controller.vo.PatientHormoneVO;
import com.mira.clinic.controller.vo.ClinicPatientPageByClinicVO;
import com.mira.api.clinic.dto.ClinicPatientPageResponseDTO;
import com.mira.clinic.service.IPatientService;
import com.mira.mybatis.response.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 病人管理控制器
 *
 * <AUTHOR>
 */
@Api(tags = "病人管理")
@RestController
@RequestMapping("/tenant/patient")
public class PatientController {
    @Resource
    private IPatientService patientService;

    @ApiOperation("查询诊所管理员的所有病人")
    @PostMapping("/clinic/patient-page")
    public PageResult<ClinicPatientPageByClinicVO> patientPageByClinic(@RequestBody ClinicPatientPageRequestDTO clinicPatientPageRequestDTO) {
        return patientService.patientPageByClinic(clinicPatientPageRequestDTO);
    }

    @ApiOperation("查询医生的所有病人")
    @PostMapping("/doctor/patient-page")
    public PageResult<ClinicPatientPageVO> patientPageByDoctor(@RequestBody ClinicDoctorPatientPageRequestDTO tenantDoctorPatientPageDTO) {
        return patientService.patientPageByDoctor(tenantDoctorPatientPageDTO);
    }



    @ApiOperation("修改病人关联的医生")
    @PostMapping("/edit/patient-doctor")
    public void editPatientDoctor(@RequestBody EditPatientDoctorDTO editPatientDoctorDTO) {
        patientService.editPatientDoctor(editPatientDoctorDTO);
    }

    @ApiOperation("分页展示病人的测试数据结果")
    @PostMapping("/data-page")
    public PageResult<PatientHormoneVO> hormoneDataPage(@RequestBody PatientDataPageDTO patientDataPageDTO) {
        return patientService.hormoneDataPage(patientDataPageDTO);
    }
}
