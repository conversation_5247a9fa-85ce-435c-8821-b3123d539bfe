package com.mira.clinic.controller.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ApiModel("病人关联列表")
public class ClinicPatientPageByClinicVO extends ClinicPatientPageVO {
    @ApiModelProperty("关联医生列表")
    private List<Doctor> doctors;

    @Getter
    @Setter
    @ApiModel("Doctor")
    public static class Doctor {
        @ApiModelProperty("id")
        private Long id;

        @ApiModelProperty("医生/护士名称")
        private String name;

        @ApiModelProperty("医生/护士email")
        private String email;
    }
}
