package com.mira.clinic.async;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.clinic.dto.InviteClinicDTO;
import com.mira.api.clinic.dto.TenantDoctorDTO;
import com.mira.api.message.dto.ClinicUserEmailDTO;
import com.mira.api.message.dto.CommonEmailDTO;
import com.mira.api.message.dto.SendEmailDTO;
import com.mira.api.message.enums.EmailRoleEnum;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.user.dto.user.AppUserDTO;
import com.mira.api.user.dto.user.AppUserInfoDTO;
import com.mira.clinic.async.emailFactory.InviteClinicEmailTempFactory;
import com.mira.clinic.async.emailFactory.ResetPasswordEmailTempFactory;
import com.mira.clinic.controller.dto.InvitePatientDTO;
import com.mira.clinic.dal.entity.AppTenantDoctorEntity;
import com.mira.clinic.dal.entity.AppTenantEntity;
import com.mira.api.clinic.dto.NursePatientEmailDTO;
import com.mira.clinic.dto.TenantInfoDTO;
import com.mira.clinic.factory.email.InviteDoctorEmailFactory;
import com.mira.clinic.factory.email.InvitePatientEmailFactory;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.UserTypeEnum;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.ThreadPoolUtil;
import com.mira.web.properties.RsaProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 邮件发送生产者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@SuppressWarnings("all")
public class EmailProducer {
    @Resource
    private RsaProperties rsaProperties;

    @Resource
    private IMessageProvider messageProvider;

    /**
     * 发送病人信息的邮件给护士
     *
     * @param commonEmailDTO       邮件信息
     * @param nursePatientEmailDTO 病人信息
     */
    public void sendNursePatientEmail(String timeZone, NursePatientEmailDTO nursePatientEmailDTO, EmailTypeEnum emailTypeEnum) {
        Map<String, String> emailVariable = new HashMap<>();
        emailVariable.put("patientNumber", nursePatientEmailDTO.getPatientNumber());
        emailVariable.put("id", nursePatientEmailDTO.getNurseId().toString());

        CommonEmailDTO commonEmailDTO = new CommonEmailDTO();
        commonEmailDTO.setId(nursePatientEmailDTO.getNurseId());
        commonEmailDTO.setTimeZone(timeZone);
        commonEmailDTO.setToEmail(nursePatientEmailDTO.getNurseEmail());
        commonEmailDTO.setEmailVariable(emailVariable);
        commonEmailDTO.setEmailTypeEnum(emailTypeEnum);
        commonEmailDTO.setEmailRoleEnum(EmailRoleEnum.NURSE);

        SendEmailDTO<CommonEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(commonEmailDTO);
        sendEmailDTO.setType(0);
        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
    }

    /**
     * 邀请医生
     *
     * @param appTenantDoctor 医生信息
     * @param appTenant       诊所信息
     */
    public Map<String, String> inviteDoctor(AppTenantDoctorEntity appTenantDoctor, AppTenantEntity appTenant, int sendFlag) {
        Map<String, String> emailVariable = InviteDoctorEmailFactory.createTemplate(appTenantDoctor, appTenant, sendFlag, rsaProperties);

        CommonEmailDTO commonEmailDTO = new CommonEmailDTO();
        commonEmailDTO.setId(appTenantDoctor.getId());
        commonEmailDTO.setTimeZone(appTenantDoctor.getTimeZone());
        commonEmailDTO.setToEmail(appTenantDoctor.getEmail());
        commonEmailDTO.setEmailVariable(emailVariable);
        commonEmailDTO.setEmailTypeEnum(EmailTypeEnum.INVITE_DOCTOR_EMAIL);
        commonEmailDTO.setEmailRoleEnum(EmailRoleEnum.DOCTOR);

        SendEmailDTO<CommonEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(commonEmailDTO);
        sendEmailDTO.setType(0);
        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }


    /**
     * 邀请病人
     *
     * @param emailTypeEnum    邮件类型
     * @param tenantInfoDTO    诊所信息
     * @param invitePatientDTO 邀请病人信息
     * @param userId           用户id
     * @param patientId        病人id
     */
    public Map<String, String> invitePatient(EmailTypeEnum emailTypeEnum,
                                             TenantInfoDTO tenantInfoDTO,
                                             InvitePatientDTO invitePatientDTO,
                                             Long userId,
                                             Long patientId) {
        Map<String, String> emailVariable = InvitePatientEmailFactory
                .createTemplate(rsaProperties, tenantInfoDTO, invitePatientDTO, userId, patientId);

        CommonEmailDTO commonEmailDTO = new CommonEmailDTO();
        commonEmailDTO.setId(tenantInfoDTO.getId());
        commonEmailDTO.setTimeZone(ContextHolder.get(HeaderConst.TIME_ZONE));
        commonEmailDTO.setToEmail(invitePatientDTO.getEmail());
        commonEmailDTO.setEmailVariable(emailVariable);
        commonEmailDTO.setEmailTypeEnum(emailTypeEnum);
        commonEmailDTO.setEmailRoleEnum(EmailRoleEnum.USER);

        SendEmailDTO<CommonEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(commonEmailDTO);
        sendEmailDTO.setType(0);
        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 发送邮件给病人
     *
     * @param appUserDTO     用户信息
     * @param appUserInfoDTO 用户详细信息
     * @param clinicName     诊所名称
     */
    public void sendEmailToPatient(AppUserDTO appUserDTO, AppUserInfoDTO appUserInfoDTO, String clinicName) {
        HashMap<String, String> emailVariable = new HashMap<>();
        emailVariable.put("userNickName", appUserInfoDTO.getNickname());
        emailVariable.put("clinicName", clinicName);

        CommonEmailDTO commonEmailDTO = new CommonEmailDTO();
        commonEmailDTO.setId(appUserDTO.getId());
        commonEmailDTO.setTimeZone(appUserDTO.getTimeZone());
        commonEmailDTO.setToEmail(appUserDTO.getEmail());
        commonEmailDTO.setEmailVariable(emailVariable);
        commonEmailDTO.setEmailTypeEnum(EmailTypeEnum.NOTIFICATION_FROM_CLINIC);
        commonEmailDTO.setEmailRoleEnum(EmailRoleEnum.DOCTOR);

        SendEmailDTO<CommonEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(commonEmailDTO);
        sendEmailDTO.setType(0);
        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
    }

    /**
     * 发送医生用户重置密码邮件
     *
     * @param appUser     用户信息
     * @param appUserInfo 用户详情
     */
    public Map<String, String> resetPassword(AppTenantDoctorEntity tenantDoctor) {
        Map<String, String> emailVariable = ResetPasswordEmailTempFactory.createTemplate(tenantDoctor, rsaProperties);
        ClinicUserEmailDTO clinicUserEmailDTO = new ClinicUserEmailDTO();
        clinicUserEmailDTO.setTenantDoctorDTO(BeanUtil.toBean(tenantDoctor, TenantDoctorDTO.class));
        clinicUserEmailDTO.setEmailTypeEnum(EmailTypeEnum.CLINIC_RESET_PASSWORD);
        clinicUserEmailDTO.setEmailVariable(emailVariable);

        SendEmailDTO<ClinicUserEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(clinicUserEmailDTO);
        sendEmailDTO.setType(1);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }

    /**
     * 发送邀请诊所用户的邮件
     * @param inviteClinicDTO
     */
    public Map<String, String> inviteClinic(InviteClinicDTO inviteClinicDTO, Long doctorId) {
        Map<String, String> emailVariable = InviteClinicEmailTempFactory.createTemplate(inviteClinicDTO, doctorId, rsaProperties);
        ClinicUserEmailDTO clinicUserEmailDTO = new ClinicUserEmailDTO();
        clinicUserEmailDTO.setTenantDoctorDTO(BeanUtil.toBean(inviteClinicDTO, TenantDoctorDTO.class));
        clinicUserEmailDTO.setEmailTypeEnum(EmailTypeEnum.CLINIC_INVITE_ACCOUNT);
        clinicUserEmailDTO.setEmailVariable(emailVariable);

        SendEmailDTO<ClinicUserEmailDTO> sendEmailDTO = new SendEmailDTO<>();
        sendEmailDTO.setUserType(UserTypeEnum.CLINIC_USER.getType());
        sendEmailDTO.setEmailDTO(clinicUserEmailDTO);
        sendEmailDTO.setType(1);

        CompletableFuture.runAsync(() -> messageProvider.sendEmail(sendEmailDTO), ThreadPoolUtil.getPool());
        return emailVariable;
    }
}
