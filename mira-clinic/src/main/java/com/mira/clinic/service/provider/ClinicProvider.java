package com.mira.clinic.service.provider;

import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.clinic.dto.*;
import com.mira.api.clinic.dto.queue.PatientNewHormoneDTO;
import com.mira.api.clinic.dto.queue.PeriodChangeDTO;
import com.mira.api.clinic.dto.queue.PregnantFlagDTO;
import com.mira.api.clinic.enums.PregnantFlagEnum;
import com.mira.api.clinic.provider.IClinicProvider;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.clinic.dal.dao.AppTenantDoctorDAO;
import com.mira.clinic.dal.entity.AppTenantDoctorEntity;
import com.mira.clinic.service.IClinicService;
import com.mira.clinic.service.IInviteService;
import com.mira.clinic.service.IPatientService;
import com.mira.core.response.CommonResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Clinic服务接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class ClinicProvider implements IClinicProvider {
    @Resource
    private IClinicService clinicService;
    @Resource
    private IPatientService patientService;
    @Resource
    private IInviteService inviteService;
    @Resource
    private AppTenantDoctorDAO appTenantDoctorDAO;

    @Deprecated
    @Override
    public CommonResult<UserClinicDTO> getClinicInfo(String email) {
        return CommonResult.OK(clinicService.getClinicInfo(email));
    }

    @Override
    public CommonResult<List<UserClinicDTO>> listClinicInfos(String email) {
        return CommonResult.OK(clinicService.listClinicInfos(email));
    }

    @Override
    public CommonResult<ClinicDTO> getClinicByCode(String tenantCode) {
        return CommonResult.OK(clinicService.getClinicByCode(tenantCode));
    }

    @Override
    public CommonResult<String> bind(Long userId, String email) {
        clinicService.bind(userId, email);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> unbind(Long userId) {
        clinicService.unbind(userId);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> bindV2(Long userId, String email, String tenantCode) {
        clinicService.bindV2(userId, email, tenantCode);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> unbindV2(Long userId, String tenantCode) {
        clinicService.unbindV2(userId, tenantCode);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> rejectInvite(Long userId, String tenantCode) {
        clinicService.rejectInvite(userId, tenantCode);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> changePeriod(PeriodChangeDTO periodChangeDTO) {
        Long userId = periodChangeDTO.getUserId();
        String timeZone = periodChangeDTO.getTimeZone();
        AlgorithmRequestTypeEnum algorithmRequestTypeEnum = AlgorithmRequestTypeEnum.get(periodChangeDTO.getAlgorithmRequestType());

        log.info("user:{} change period, algorithmRequestType:{}", userId,
                Objects.nonNull(algorithmRequestTypeEnum) ? algorithmRequestTypeEnum.getValue() : "null");
        patientService.processEditPeriod(userId, timeZone, algorithmRequestTypeEnum);

        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> handlePregnantFlag(PregnantFlagDTO pregnantFlagDTO) {
        Long userId = pregnantFlagDTO.getUserId();
        String timeZone = pregnantFlagDTO.getTimeZone();
        PregnantFlagEnum pregnantFlagEnum = pregnantFlagDTO.getPregnantFlagEnum();

        log.info("user:{} handle pregnant flag, pregnantFlag:{}", userId, pregnantFlagEnum.ordinal());
        patientService.processPregnantFlag(userId, timeZone, pregnantFlagEnum);

        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> handleNewHormone(PatientNewHormoneDTO patientNewHormoneDTO) {
        Long userId = patientNewHormoneDTO.getUserId();
        String timeZone = patientNewHormoneDTO.getTimeZone();
        EmailTypeEnum emailTypeEnum = EmailTypeEnum.get(patientNewHormoneDTO.getEmailTypeCode());

        log.info("user:{} handle new hormone, emailType:{}", userId,
                Objects.nonNull(emailTypeEnum) ? emailTypeEnum.getCode() : "null");
        patientService.processNewHormone(userId, timeZone, emailTypeEnum);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> updatePatientEmail(Long userId, String email) {
        patientService.updatePatientEmail(userId, email);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<UserPatientDTO> getUserPatientDTO(Long userId) {
        UserPatientDTO userPatientDTO = clinicService.getUserPatientDTO(userId);
        return CommonResult.OK(userPatientDTO);
    }

    @Override
    public CommonResult<List<UserPatientDTO>> listUserPatientDTOs(Long userId) {
        return CommonResult.OK(clinicService.listUserPatientDTOs(userId));
    }

    @Override
    public CommonResult<Void> initCreateClinic(InitCreateClinicDTO initCreateClinicDTO) {
        clinicService.initCreateClinic(initCreateClinicDTO);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<Void> inviteClinic(InviteClinicDTO inviteClinicDTO, int sendFlag) {
        clinicService.inviteClinic(inviteClinicDTO, sendFlag);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<ClinicPageResponseDTO> clinicPage(ClinicPageRequestDTO clinicPageRequestDTO) {
        ClinicPageResponseDTO clinicPageResponseDTO = clinicService.clinicPage(clinicPageRequestDTO);
        return CommonResult.OK(clinicPageResponseDTO);
    }

    @Override
    public CommonResult<ClinicPatientPageResponseDTO> clinicPatientPage(ClinicPatientPageRequestDTO clinicPatientPageRequestDTO) {
        ClinicPatientPageResponseDTO clinicPatientPageResponseDTO = patientService.patientPage(clinicPatientPageRequestDTO);
        return CommonResult.OK(clinicPatientPageResponseDTO);
    }

    @Override
    public CommonResult<List<ClinicListDTO>> allClinic() {
        List<ClinicListDTO> clinicListDTOS = clinicService.allClinic();
        return CommonResult.OK(clinicListDTOS);
    }

    @Override
    public CommonResult<Void> editClinicInfo(EditClinicDTO editClinicDTO) {
        clinicService.editClinicInfo(editClinicDTO);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<Void> deleteClinicInfo(Long id) {
        clinicService.deleteClinicInfo(id);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> applyPatient(ApplyPatientDTO applyPatientDTO) {
        inviteService.applyPatient(applyPatientDTO);
        return CommonResult.OK();
    }

    @Override
    public CommonResult<String> resetClinicPassword(String email) {
        String errMessage=null;
        String newPassword="asrD@3458";
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getByEmail(email);
        if (Objects.isNull(appTenantDoctor)) {
            errMessage= "not exist a clinic account with email "+email;
        }else {
            String salt = RandomStringUtils.randomAlphanumeric(20);
            appTenantDoctor.setPassword(new Sha256Hash(newPassword, salt).toHex());
            appTenantDoctor.setSalt(salt);
            appTenantDoctorDAO.updateById(appTenantDoctor);
        }
        return CommonResult.OK(errMessage);
    }
}
