package com.mira.clinic.service.util;

import java.util.Random;

public class InitPatientNumberUtil {
    public static String generatePatientNumber(Long existCount) {
        return "N" + existCount;
    }

    public static String generateRandomString(int length) {
        String charPool = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder sb = new StringBuilder();
        Random random = new Random();

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(charPool.length()); // 随机选取字符池中的一个索引
            sb.append(charPool.charAt(index)); // 将选取的字符追加到StringBuilder对象中
        }

        return sb.toString(); // 将StringBuilder对象转换成字符串并返回
    }
}
