package com.mira.clinic.service;

import com.mira.api.clinic.dto.*;
import com.mira.clinic.controller.dto.*;
import com.mira.clinic.controller.vo.TenantDoctorPageVO;
import com.mira.clinic.controller.vo.TenantDoctorVO;
import com.mira.clinic.controller.vo.TenantSettingVO;
import com.mira.mybatis.response.PageResult;

import java.util.List;

/**
 * Clinic接口
 *
 * <AUTHOR>
 */
public interface IClinicService {
    /**
     * 获取诊所信息
     *
     * @param email 邮箱
     * @return UserClinicDTO
     * @deprecated since 7.6.27
     */
    UserClinicDTO getClinicInfo(String email);

    List<UserClinicDTO> listClinicInfos(String email);

    /**
     * 绑定诊所
     *
     * @param userId 用户id
     * @param email  邮箱
     */
    @Deprecated(since = "7.6.27")
    void bind(Long userId, String email);

    /**
     * 解除与诊所的绑定
     */
    @Deprecated(since = "7.6.27")
    void unbind(Long userId);

    void bindV2(Long userId, String email, String tenantCode);

    void unbindV2(Long userId, String tenantCode);

    void rejectInvite(Long userId, String tenantCode);

    ClinicDTO getClinicByCode(String tenantCode);

    /**
     * 医生与护士分页列表
     *
     * @param tenantDoctorPageDTO tenantDoctorPageDTO
     * @return PageResult<TenantDoctorPageVO>
     */
    PageResult<TenantDoctorPageVO> doctorPage(TenantDoctorPageDTO tenantDoctorPageDTO);

    /**
     * 医生与护士列表
     *
     * @param tenantDoctorListDTO tenantDoctorListDTO
     * @return List<TenantDoctorVO>
     */
    List<TenantDoctorVO> doctorList(TenantDoctorListDTO tenantDoctorListDTO);

    /**
     * 删除医生/护士
     *
     * @param tenantId tenantId
     */
    void doctorRemove(Long tenantId);

    /**
     * 删除病人
     *
     * @param patientId patientId
     */
    void patientRemove(Long patientId);


    /**
     * setting设置
     *
     * @param editSettingDTO editSettingDTO
     */
    void editSetting(EditSettingDTO editSettingDTO);

    /**
     * setting查询
     *
     * @return TenantSettingVO
     */
    TenantSettingVO setting();

    /**
     * 获取用户作为病人的信息
     *
     * @param userId
     * @return
     * @deprecated since 7.6.27
     */
    UserPatientDTO getUserPatientDTO(Long userId);

    List<UserPatientDTO> listUserPatientDTOs(Long userId);

    /**
     * 初始创建诊所
     *
     * @param initCreateClinicDTO
     */
    void initCreateClinic(InitCreateClinicDTO initCreateClinicDTO);

    /**
     * 邀请诊所
     *
     * @param inviteClinicDTO
     * @param sendFlag
     */
    void inviteClinic(InviteClinicDTO inviteClinicDTO, int sendFlag);

    /**
     * 获取诊所分页展示数据
     *
     * @param clinicPageRequestDTO
     * @return
     */
    ClinicPageResponseDTO clinicPage(ClinicPageRequestDTO clinicPageRequestDTO);

    /**
     * all clinic
     */
    List<ClinicListDTO> allClinic();

    /**
     * 编辑诊所
     *
     * @param editClinicDTO
     */
    void editClinicInfo(EditClinicDTO editClinicDTO);

    /**
     * 删除诊所
     *
     * @param id
     */
    void deleteClinicInfo(Long id);

    /**
     * 修改诊所名称
     *
     * @param name
     */
    void editName(String name);
}
