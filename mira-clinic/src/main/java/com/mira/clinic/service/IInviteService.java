package com.mira.clinic.service;

import com.mira.api.clinic.dto.ApplyPatientDTO;
import com.mira.clinic.controller.dto.InitDoctorDTO;
import com.mira.clinic.controller.dto.InviteDoctorDTO;
import com.mira.clinic.controller.dto.InviteNurseDTO;
import com.mira.clinic.controller.dto.InvitePatientDTO;

/**
 * 邀请医生/护士/病人接口
 *
 * <AUTHOR>
 */
public interface IInviteService {
    /**
     * 邀请Doctor
     *
     * @param inviteDoctorDTO 邀请医生DTO
     */
    void inviteDoctor(InviteDoctorDTO inviteDoctorDTO, int sendFlag);



    /**
     * 初始化Doctor
     *
     * @param initDoctorDTO 初始化医生DTO
     */
    void initDoctor(InitDoctorDTO initDoctorDTO);



    /**
     * 邀请Patient
     *
     * @param invitePatientDTO 邀请病人DTO
     */
    void invitePatient(InvitePatientDTO invitePatientDTO, int sendFlag);

    /**
     * 用户授权同意成为病人
     *
     * @param hash 授权hash
     */
    void grantPatient(String hash);

    /**
     * 批量创建病人（仅开发人员使用）
     */
    void batchAddPatient();

    void applyPatient(ApplyPatientDTO applyPatientDTO);
}
