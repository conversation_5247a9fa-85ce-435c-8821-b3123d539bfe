package com.mira.clinic.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import com.mira.api.clinic.dto.*;
import com.mira.api.clinic.enums.ClinicPatientStatusEnum;
import com.mira.api.clinic.enums.ClinicianStatusEnum;
import com.mira.api.clinic.enums.PregnantFlagEnum;
import com.mira.api.message.enums.EmailTypeEnum;
import com.mira.clinic.async.EmailProducer;
import com.mira.clinic.controller.dto.EditPatientDoctorDTO;
import com.mira.clinic.controller.dto.PatientDataPageDTO;
import com.mira.clinic.controller.dto.ClinicDoctorPatientPageRequestDTO;
import com.mira.clinic.controller.vo.ClinicPatientPageVO;
import com.mira.clinic.controller.vo.PatientHormoneVO;
import com.mira.clinic.controller.vo.ClinicPatientPageByClinicVO;
import com.mira.clinic.dal.dao.*;
import com.mira.clinic.dal.entity.*;
import com.mira.clinic.dto.TenantDoctorPatientListDTO;
import com.mira.api.clinic.dto.ClinicPatientListDTO;
import com.mira.clinic.enums.ClinicRoleEnum;
import com.mira.clinic.exception.ClinicException;
import com.mira.clinic.properties.CacheExpireProperties;
import com.mira.clinic.service.IPatientService;
import com.mira.clinic.service.manager.CacheManager;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.BizCodeEnum;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.util.JsonUtil;
import com.mira.mybatis.response.PageResult;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 病人相关业务接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PatientServiceImpl implements IPatientService {
    @Resource
    private AppTenantDoctorPatientDAO appTenantDoctorPatientDAO;

    @Resource
    private AppTenantSettingDAO appTenantSettingDAO;
    @Resource
    private AppTenantDoctorDAO appTenantDoctorDAO;
    @Resource
    private AppTenantPatientDAO appTenantPatientDAO;

    @Resource
    private CacheManager cacheManager;

    @Resource
    private EmailProducer emailProducer;

    @Resource
    private CacheExpireProperties cacheExpireProperties;

    @Override
    public void processEditPeriod(Long userId, String timeZone, AlgorithmRequestTypeEnum algorithmRequestTypeEnum) {
        List<NursePatientEmailDTO> sendEmailList = new ArrayList<>();
        if (sendEmailList.isEmpty()) {
            return;
        }
        switch (algorithmRequestTypeEnum) {
            case CONFIRM_PERIOD:
                sendEmail(timeZone, sendEmailList, EmailTypeEnum.NURSE_PERIOD_STARTED);
                break;
            case EDIT_PERIOD:
            case EXTEND_PERIOD:
            case EDIT_PERIOD_LENGTH:
            case EDIT_CYCLE_LENGTH:
                sendEmail(timeZone, sendEmailList, EmailTypeEnum.NURSE_PERIOD_UPDATED);
                break;
            default:
                break;
        }
    }

    @Override
    public void processPregnantFlag(Long userId, String timeZone, PregnantFlagEnum pregnantFlagEnum) {
        List<NursePatientEmailDTO> sendEmailList = new ArrayList<>();
        if (sendEmailList.isEmpty()) {
            return;
        }
        switch (pregnantFlagEnum) {
            case CUSTOM_LOG:
                sendEmail(timeZone, sendEmailList, EmailTypeEnum.NURSE_PREGNANT_LOG);
                break;
            case HCG:
            case HCG_QUALITATIVE:
                sendEmail(timeZone, sendEmailList, EmailTypeEnum.NURSE_POSITIVE_HCG);
                break;
            default:
                break;
        }
    }

    @Override
    public void processNewHormone(Long userId, String timeZone, EmailTypeEnum emailTypeEnum) {
        List<NursePatientEmailDTO> sendEmailList = new ArrayList<>();
        if (sendEmailList.isEmpty()) {
            return;
        }

        sendEmail(timeZone, sendEmailList, emailTypeEnum);
    }


    private void sendEmail(String timeZone, List<NursePatientEmailDTO> sendEmailList, EmailTypeEnum mailTypeEnum) {
        for (NursePatientEmailDTO nursePatientEmailDTO : sendEmailList) {
            if (EmailTypeEnum.NURSE_PERIOD_UPDATED.equals(mailTypeEnum)) {
                // 3小时内有同类型email发送过，则不再发送
                boolean exist = cacheManager.checkExistNurseEmailHistory(EmailTypeEnum.NURSE_PERIOD_UPDATED,
                        nursePatientEmailDTO.getNurseId(), nursePatientEmailDTO.getPatientId());
                if (exist) {
                    log.info("existed mail type:{}, nurse:{},patient:{}",
                            EmailTypeEnum.NURSE_PERIOD_UPDATED.getCode(), nursePatientEmailDTO.getNurseEmail(), nursePatientEmailDTO.getPatientId());
                    continue;
                }
            }
            boolean sendFlag = getSendFlag(mailTypeEnum, nursePatientEmailDTO.getTenantCode());
            if (!sendFlag) {
                continue;
            }

            log.info("send email: mail type:{}, nurse:{},patient:{}",
                    mailTypeEnum.getCode(), nursePatientEmailDTO.getNurseEmail(), nursePatientEmailDTO.getPatientId());

            // send email
            emailProducer.sendNursePatientEmail(timeZone, nursePatientEmailDTO, mailTypeEnum);
        }
    }

    private boolean getSendFlag(EmailTypeEnum mailTypeEnum, String tenantCode) {
        boolean sendFlag = true;
        AppTenantSettingEntity tenantSettingEntity = appTenantSettingDAO.getByTenantCode(tenantCode);
        if (tenantSettingEntity == null) {
            return sendFlag;
        }
        switch (mailTypeEnum) {
            case NURSE_PERIOD_STARTED:
                if (0 == tenantSettingEntity.getNotificationPeriodStarted()) {
                    sendFlag = false;
                }
                break;
            case NURSE_PERIOD_UPDATED:
                if (0 == tenantSettingEntity.getNotificationPeriodEdited()) {
                    sendFlag = false;
                }
                break;
            case NURSE_FIRST_TEST:
                if (0 == tenantSettingEntity.getNotificationFirstTest()) {
                    sendFlag = false;
                }
                break;
            case NURSE_LH_SURGE:
                if (0 == tenantSettingEntity.getNotificationLhSurge()) {
                    sendFlag = false;
                }
                break;
            case NURSE_POSITIVE_HCG:
                if (0 == tenantSettingEntity.getNotificationPositiveHCG()) {
                    sendFlag = false;
                }
                break;
            case NURSE_PREGNANT_LOG:
                if (0 == tenantSettingEntity.getNotificationPregnant()) {
                    sendFlag = false;
                }
                break;
            default:
                break;
        }
        return sendFlag;
    }

    @Override
    public PageResult<ClinicPatientPageByClinicVO> patientPageByClinic(ClinicPatientPageRequestDTO pageDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String tenantCode = appTenantDoctor.getTenantCode();

        // 首先获取病人的分页列表
        int currIndex = (pageDTO.getCurrent() - 1) * pageDTO.getSize();
        List<ClinicPatientListDTO> clinicPatientListDTOS = appTenantPatientDAO
                .pageTenantPatientByClinic(tenantCode, currIndex, pageDTO.getSize(), pageDTO.getKeyword());
        buildPatientStatus(clinicPatientListDTOS);
        List<Long> patientIds = clinicPatientListDTOS.stream()
                                                     .map(ClinicPatientListDTO::getId)
                                                     .collect(Collectors.toList());
        // 获取该页病人对应的所有医生病人关系
        List<TenantDoctorPatientListDTO> tenantDoctorPatientListDTOS = CollectionUtils.isEmpty(patientIds)
                ? new ArrayList<>() : appTenantDoctorPatientDAO.listTenantDoctorPatientListDTO(patientIds);
        List<Long> doctorIds = tenantDoctorPatientListDTOS.stream()
                                                          .map(TenantDoctorPatientListDTO::getDoctorId)
                                                          .collect(Collectors.toList());
        Integer count = appTenantPatientDAO.countTenantPatientByClinic(tenantCode, pageDTO.getKeyword());

        List<ClinicPatientPageByClinicVO> tenantPatientPageByClinicVOS = new ArrayList<>();
        // 对所有病人id进行遍历
        for (Long patientId : patientIds) {
            List<TenantDoctorPatientListDTO> selectTenantDoctorPatientListDTOS =
                    tenantDoctorPatientListDTOS.stream()
                                               .filter(tenantDoctorPatientListDTO -> patientId.equals(tenantDoctorPatientListDTO.getPatientId()))
                                               .collect(Collectors.toList());
            List<ClinicPatientPageByClinicVO.Doctor> doctors = new ArrayList<>();
            // 对所有医生病人关系对象进行遍历
            for (TenantDoctorPatientListDTO doctorPatientListDTO : selectTenantDoctorPatientListDTOS) {
                ClinicPatientPageByClinicVO.Doctor doctor = new ClinicPatientPageByClinicVO.Doctor();
                Long doctorId = doctorPatientListDTO.getDoctorId();
                doctor.setId(doctorId);
                doctor.setEmail(doctorPatientListDTO.getEmail());
                doctor.setName(doctorPatientListDTO.getName());
                doctors.add(doctor);
            }
            ClinicPatientListDTO patientListDTO = clinicPatientListDTOS.stream()
                                                                       .filter(clinicPatientListDTO -> clinicPatientListDTO.getId().equals(patientId))
                                                                       .findFirst()
                                                                       .orElse(null);
            if (patientListDTO == null) {
                continue;
            }
            ClinicPatientPageByClinicVO tenantPatientPageByClinicVO = BeanUtil.toBean(patientListDTO, ClinicPatientPageByClinicVO.class);
            tenantPatientPageByClinicVO.setDoctors(doctors);
            tenantPatientPageByClinicVOS.add(tenantPatientPageByClinicVO);
        }

        return new PageResult<>(tenantPatientPageByClinicVOS, count, pageDTO.getSize(), pageDTO.getCurrent());
    }

    @Override
    public ClinicPatientPageResponseDTO patientPage(ClinicPatientPageRequestDTO clinicPatientPageRequestDTO) {
        String clinicCode = clinicPatientPageRequestDTO.getClinicCode();
        String keyword = clinicPatientPageRequestDTO.getKeyword();
        Integer current = clinicPatientPageRequestDTO.getCurrent();
        Integer size = clinicPatientPageRequestDTO.getSize();
        int currIndex = (current - 1) * size;

        List<ClinicPatientListDTO> clinicPatientListDTOS = appTenantPatientDAO
                .pageTenantPatientByClinic(clinicCode, currIndex, size, keyword);
        buildPatientStatus(clinicPatientListDTOS);
        Integer count = appTenantPatientDAO.countTenantPatientByClinic(clinicCode, keyword);
        ClinicPatientPageResponseDTO clinicPatientPageResponseDTO = new ClinicPatientPageResponseDTO();
        clinicPatientPageResponseDTO.setClinicPatientDTOs(clinicPatientListDTOS);
        clinicPatientPageResponseDTO.setTotal(count);
        return clinicPatientPageResponseDTO;
    }



    @Override
    public PageResult<ClinicPatientPageVO> patientPageByDoctor(ClinicDoctorPatientPageRequestDTO pageDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        String tenantCode = appTenantDoctor.getTenantCode();

        int currIndex = (pageDTO.getCurrent() - 1) * pageDTO.getSize();
        List<ClinicPatientListDTO> clinicPatientListDTOS = appTenantPatientDAO
                .pageTenantPatientByDoctor(tenantCode, pageDTO.getDoctorId(), currIndex, pageDTO.getSize(), pageDTO.getKeyword());
        buildPatientStatus(clinicPatientListDTOS);
        Integer count = appTenantPatientDAO
                .countTenantPatientByDoctors(tenantCode, Collections.singletonList(pageDTO.getDoctorId()), pageDTO.getKeyword());


        List<ClinicPatientPageVO> clinicPatientPageVOS = new ArrayList<>();
        // 对所有病人id进行遍历
        for (ClinicPatientListDTO clinicPatientListDTO : clinicPatientListDTOS) {
            ClinicPatientPageVO clinicPatientPageVO = BeanUtil.toBean(clinicPatientListDTO, ClinicPatientPageVO.class);
            clinicPatientPageVOS.add(clinicPatientPageVO);
        }

        return new PageResult<>(clinicPatientPageVOS, count, pageDTO.getSize(), pageDTO.getCurrent());
    }

    private void buildPatientStatus(List<ClinicPatientListDTO> clinicPatientListDTOS) {
        for (ClinicPatientListDTO clinicPatientListDTO : clinicPatientListDTOS) {
            Integer patientStatus = clinicPatientListDTO.getPatientStatus();
            Long id = clinicPatientListDTO.getId();
            Integer userStatus = clinicPatientListDTO.getUserStatus();
            Integer userDeleted = clinicPatientListDTO.getUserDeleted();
            Integer fresh = clinicPatientListDTO.getFresh();
            Long userId = clinicPatientListDTO.getUserId();

            //用户的mira app账号已删除
            if (userDeleted != null && userDeleted == 1) {
                clinicPatientListDTO.setPatientStatus(ClinicPatientStatusEnum.DELETED.getCode());
                clinicPatientListDTO.setEmail("Deleted");
                continue;
            }
            //用户的mira app账号未走完注册流程
            if (fresh == null && userStatus != null &&  userStatus == 1) {
                clinicPatientListDTO.setPatientStatus(ClinicPatientStatusEnum.REGISTERING.getCode());
                continue;
            }

            if (userId == null) {
                //邀请中或者邀请过期, 直接看数据库 patientStatus 状态
                //clinicPatientListDTO.setPatientStatus(ClinicPatientStatusEnum.INVITING.getCode());
            }

            //将处在邀请中的账号，校验是否邀请过期
            if(ClinicPatientStatusEnum.INVITING.getCode().equals(patientStatus)) {
                AppTenantPatientEntity appTenantPatientEntity = appTenantPatientDAO.getById(id);
                Long inviteTime = appTenantPatientEntity.getInviteTime();
                if(inviteTime == null) {
                    continue;
                }
                if (inviteTime + cacheExpireProperties.getInvite() * 60 * 1000 < System.currentTimeMillis()) {
                    appTenantPatientEntity.setStatus(ClinicianStatusEnum.INVITATION_EXPIRED.getCode());
                    appTenantPatientDAO.updateById(appTenantPatientEntity);
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editPatientDoctor(EditPatientDoctorDTO editPatientDoctorDTO) {
        Long id = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        AppTenantDoctorEntity appTenantDoctor = appTenantDoctorDAO.getById(id);
        if (!ClinicRoleEnum.CLINIC_ADMIN.getCode().equals(appTenantDoctor.getRole())) {
            throw new ClinicException(BizCodeEnum.WITHOUT_PERMISSION);
        }
        String tenantCode = appTenantDoctor.getTenantCode();
        Long patientId = editPatientDoctorDTO.getPatientId();
        List<Long> paramTenantIds = editPatientDoctorDTO.getTenantIds();

        List<AppTenantDoctorPatientEntity> dbDoctorPatientEntities = appTenantDoctorPatientDAO.listByPatientId(patientId);
        List<AppTenantDoctorPatientEntity> addList = new ArrayList<>();
        // 数据库不存在关联的医生
        if (dbDoctorPatientEntities.isEmpty()) {
            if (CollectionUtils.isNotEmpty(paramTenantIds)) {
                // add all
                for (Long tenantId : paramTenantIds) {
                    AppTenantDoctorPatientEntity tenantDoctorPatientEntity = new AppTenantDoctorPatientEntity();
                    tenantDoctorPatientEntity.setTenantCode(tenantCode);
                    tenantDoctorPatientEntity.setPatientId(patientId);
                    tenantDoctorPatientEntity.setDoctorId(tenantId);
                    UpdateEntityTimeUtil.setBaseEntityTime(timeZone, tenantDoctorPatientEntity);
                    addList.add(tenantDoctorPatientEntity);
                }
                if (!addList.isEmpty()) {
                    appTenantDoctorPatientDAO.saveBatch(addList);
                }
            }
            return;
        }

        // 数据库存在关联的医生
        List<Long> dbDoctorIds = dbDoctorPatientEntities.stream()
                                                        .map(AppTenantDoctorPatientEntity::getDoctorId)
                                                        .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(paramTenantIds)) {
            // delete all
            appTenantDoctorPatientDAO.removeInDoctorPatient(patientId, dbDoctorIds);
            return;
        }

        // tenantIds not null && dbDoctorIds not null
        List<Long> tenantIds2 = new ArrayList<>(paramTenantIds);
        paramTenantIds.removeAll(dbDoctorIds);
        // 剩下的paramTenantIds需要被添加
        // add all
        for (Long tenantId : paramTenantIds) {
            AppTenantDoctorPatientEntity tenantDoctorPatientEntity = new AppTenantDoctorPatientEntity();
            tenantDoctorPatientEntity.setTenantCode(tenantCode);
            tenantDoctorPatientEntity.setPatientId(patientId);
            tenantDoctorPatientEntity.setDoctorId(tenantId);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, tenantDoctorPatientEntity);
            addList.add(tenantDoctorPatientEntity);
        }
        if (!addList.isEmpty()) {
            appTenantDoctorPatientDAO.saveBatch(addList);
        }

        dbDoctorIds.removeAll(tenantIds2);
        // 剩下的dbDoctorIds需要被删除
        if (CollectionUtils.isNotEmpty(dbDoctorIds)) {
            appTenantDoctorPatientDAO.removeInDoctorPatient(patientId, dbDoctorIds);
        }
    }

    @Override
    public PageResult<PatientHormoneVO> hormoneDataPage(PatientDataPageDTO pageDTO) {
        Long patientId = pageDTO.getPatientId();
        AppTenantPatientEntity tenantPatientEntity = appTenantPatientDAO.getById(patientId);
        Long userId = tenantPatientEntity.getUserId();
        List<PatientHormoneVO> patientHormoneVOS = getResults(userId);

        if (CollectionUtils.isEmpty(patientHormoneVOS)) {
            return new PageResult<>();
        }
        Integer size = pageDTO.getSize();
        Integer current = pageDTO.getCurrent();
        int total = patientHormoneVOS.size();
        int endIndex = Math.min(current * size, total);
        int startIndex = (current - 1) * size;

        if (startIndex > endIndex) {
            return new PageResult<>();
        }
        List<PatientHormoneVO> subPatientHormoneVOS = patientHormoneVOS.subList(startIndex, endIndex);

        return new PageResult<>(subPatientHormoneVOS, total, size, current);
    }

    private List<PatientHormoneVO> getResults(Long userId) {
        AlgorithmResultDTO cacheAlgorithmResult = cacheManager.getCacheAlgorithmResult(userId);
        if (ObjectUtils.isEmpty(cacheAlgorithmResult)) {
            return null;
        }

        List<HormoneDTO> hormoneDatas = JsonUtil.toArray(cacheAlgorithmResult.getHormoneData(), HormoneDTO.class);
        List<PatientHormoneVO> patientHormoneVOS = new ArrayList<>();
        for (HormoneDTO hormoneDTO : hormoneDatas) {
            String testTime = hormoneDTO.getTest_time();
            HormoneDTO.TestResult testResult = hormoneDTO.getTest_results();
            Integer wandType = testResult.getWand_type();
            if (StringUtils.isBlank(testResult.getEcode()) || testResult.getEcode().startsWith("B")) {
                PatientHormoneVO patientHormoneVO = new PatientHormoneVO();
                patientHormoneVO.setTestTime(testTime);
                if (WandTypeEnum.LH.getInteger().equals(wandType)) {
                    patientHormoneVO.setLH(testResult.getValue1());
                    patientHormoneVOS.add(patientHormoneVO);
                } else if (WandTypeEnum.E3G_LH.getInteger().equals(wandType)) {
                    // 显示两条
                    patientHormoneVO.setE3G(testResult.getValue1());
                    patientHormoneVO.setLH(testResult.getValue2());
                    patientHormoneVOS.add(patientHormoneVO);
                } else if (WandTypeEnum.HCG.getInteger().equals(wandType)) {
                    patientHormoneVO.setHCG(testResult.getValue1());
                    patientHormoneVOS.add(patientHormoneVO);
                } else if (WandTypeEnum.PDG.getInteger().equals(wandType)) {
                    patientHormoneVO.setPDG(testResult.getValue1());
                    patientHormoneVOS.add(patientHormoneVO);
                } else if (WandTypeEnum.E3G_HIGH_RANGE.getInteger().equals(wandType)) {
                    patientHormoneVO.setE3G(testResult.getValue1());
                    patientHormoneVOS.add(patientHormoneVO);
                } else if (WandTypeEnum.LH_E3G_PDG.getInteger().equals(wandType)) {
                    patientHormoneVO.setE3G(testResult.getValue3());
                    patientHormoneVO.setLH(testResult.getValue1());
                    patientHormoneVO.setPDG(testResult.getValue2());
                    patientHormoneVOS.add(patientHormoneVO);
                } else if (WandTypeEnum.HCG_QUALITATIVE.getInteger().equals(wandType)) {
                    Float value1 = testResult.getValue1();
                    String valueResult1 = "Pregnant";
                    if (value1 < 10) {
                        valueResult1 = "Not Pregnant";
                    }
                    patientHormoneVO.setHCG2(valueResult1);
                    patientHormoneVOS.add(patientHormoneVO);
                } else if (WandTypeEnum.FSH.getInteger().equals(wandType)) {
                    patientHormoneVO.setFSH(testResult.getValue1());
                    patientHormoneVOS.add(patientHormoneVO);
                }

            }
        }
        if (!patientHormoneVOS.isEmpty()) {
            Collections.reverse(patientHormoneVOS);
        }
        return patientHormoneVOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePatientEmail(Long userId, String email) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        List<AppTenantPatientEntity> appTenantPatientEntities = appTenantPatientDAO.listByUserId(userId);
        if (CollectionUtils.isNotEmpty(appTenantPatientEntities)) {
            for (AppTenantPatientEntity appTenantPatientEntity : appTenantPatientEntities) {
                appTenantPatientEntity.setInitEmail(email);
                UpdateEntityTimeUtil.updateBaseEntityTime(timeZone, appTenantPatientEntity);
                appTenantPatientDAO.updateById(appTenantPatientEntity);
            }
        }
    }
}
