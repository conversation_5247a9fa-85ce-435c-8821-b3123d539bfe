<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.clinic.dal.mapper.AppTenantMapper">
    <select id="listByPage" resultType="com.mira.api.clinic.dto.ClinicListDTO">
        select
        t.id,
        t.code as 'tenant_code',
        t.name as 'tenant_name',
        t.type as 'tenant_type',
        t.icon,
        t.notification_icon,
        t.init_password,
        t.description as 'website_url',
        t.create_time_str,
        t.modify_time_str,
        td.id as 'doctor_id',
        td.email,
        td.status,
        td.invite_time
        from app_tenant t
        LEFT JOIN app_tenant_doctor td on t.`code`=td.tenant_code
        where td.doctor_type=0
        and t.deleted=0
        <include refid="listClinic-if"></include>
        order by t.create_time desc
        limit #{currIndex} , #{pageSize}
    </select>

    <select id="all" resultType="com.mira.api.clinic.dto.ClinicListDTO">
        select
        t.id,
        t.code as 'tenant_code',
        t.name as 'tenant_name',
        t.type as 'tenant_type',
        t.icon,
        t.notification_icon,
        t.init_password,
        t.description as 'website_url',
        t.create_time_str,
        t.modify_time_str
        from app_tenant t
        where t.deleted=0
        order by t.create_time desc
    </select>

    <select id="countByKeyword" resultType="java.lang.Long">
        select count(1)
        from app_tenant t
        LEFT JOIN app_tenant_doctor td on t.`code`=td.tenant_code
        where td.doctor_type=0
        and t.deleted=0
        <include refid="listClinic-if"></include>
    </select>


    <sql id="listClinic-if">
        <if test="keyWord!=null and keyWord != ''">
            and (t.name like concat('%', #{keyWord,jdbcType=VARCHAR}, '%')
            or td.email like concat('%', #{keyWord,jdbcType=VARCHAR}, '%')
            )
        </if>
    </sql>


</mapper>
