<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.mira.clinic.dal.mapper.AppTenantPatientMapper">
    <select id="listTenantPatientByClinic" resultType="com.mira.api.clinic.dto.ClinicPatientListDTO">
        select tp.id,
               tp.user_id,
               tp.`status`                    as 'patient_status',
               tp.modify_time,
               IFNULL(u.email, tp.init_email) as email,
               u.mobile,
               u.status                       as 'user_status',
               u.deleted                      as 'user_deleted',
               tp.nickname,
               tp.patient_number,
               up.fresh
        from app_tenant_patient tp
                 LEFT JOIN app_user u on tp.user_id = u.id
                 LEFT JOIN app_user_period up on up.user_id = u.id
        where tp.tenant_code = #{tenantCode}
          and tp.`deleted` = 0
    </select>

    <select id="pageTenantPatientByClinic" resultType="com.mira.api.clinic.dto.ClinicPatientListDTO">
        select tp.id,
        tp.user_id,
        tp.`status` as 'patient_status',
        tp.modify_time,
        IFNULL(u.email, tp.init_email) as email,
        u.mobile,
        u.status as 'user_status',
        u.deleted as 'user_deleted',
        tp.nickname,
        tp.patient_number,
        up.fresh
        from app_tenant_patient tp
        LEFT JOIN app_user u on tp.user_id = u.id
        LEFT JOIN app_user_period up on up.user_id = u.id
        where tp.tenant_code = #{tenantCode}
        and tp.`deleted` = 0
        <include refid="listTenantPatient-if"></include>
        order by tp.modify_time desc
        limit #{currIndex} , #{pageSize}
    </select>

    <select id="countTenantPatientByClinic" resultType="java.lang.Integer">
        select count(1) from app_tenant_patient tp
        LEFT JOIN app_user u on tp.user_id = u.id
        LEFT JOIN app_user_period up on up.user_id = u.id
        where tp.tenant_code = #{tenantCode}
        and tp.`deleted` = 0
        <include refid="listTenantPatient-if"></include>
    </select>

    <select id="pageTenantPatientByDoctor" resultType="com.mira.api.clinic.dto.ClinicPatientListDTO">
        select tp.id,
        tp.user_id,
        tp.`status` as 'patient_status',
        tp.modify_time,
        IFNULL(u.email, tp.init_email) as email,
        u.mobile,
        u.status as 'user_status',
        u.deleted as 'user_deleted',
        tp.nickname,
        tp.patient_number,
        up.fresh
        from app_tenant_patient tp
        LEFT JOIN app_tenant_doctor_patient tdp on tdp.patient_id = tp.id
        LEFT JOIN app_user u on tp.user_id = u.id
        LEFT JOIN app_user_period up on up.user_id = u.id
        where tp.tenant_code = #{tenantCode}
        and tp.`deleted` = 0
        and tdp.doctor_id = #{doctorId}
        <include refid="listTenantPatient-if"></include>
        order by tp.modify_time desc
        limit #{currIndex} , #{pageSize}
    </select>

    <select id="pageTenantPatientByDoctors" resultType="com.mira.api.clinic.dto.ClinicPatientListDTO">
        select distinct tp.id,
        tp.user_id,
        tp.`status` as 'patient_status',
        tp.modify_time,
        IFNULL(u.email, tp.init_email) as email,
        u.mobile,
        u.status as 'user_status',
        u.deleted as 'user_deleted',
        tp.nickname,
        tp.patient_number,
        up.fresh
        from app_tenant_patient tp
        LEFT JOIN app_tenant_doctor_patient tdp on tdp.patient_id = tp.id
        LEFT JOIN app_user u on tp.user_id = u.id
        LEFT JOIN app_user_period up on up.user_id = u.id
        where tp.tenant_code = #{tenantCode}
        and tp.`deleted` = 0
        and tdp.doctor_id in
        <foreach item="doctorId" index="index" collection="doctorIds" open="(" separator="," close=")">
            #{doctorId}
        </foreach>
        <include refid="listTenantPatient-if"></include>
        order by tp.modify_time desc
        limit #{currIndex} , #{pageSize}
    </select>

    <select id="listByPatientEmail" resultType="com.mira.clinic.dto.AppTenantPatientDTO">
        select patient.id,
               patient.tenant_code,
               patient.patient_number,
               patient.init_email,
               patient.nickname,
               patient.first_name,
               patient.last_name,
               patient.user_id,
               patient.status,
               patient.create_time,
               patient.modify_time,
               tenant.id as tenantId
        from app_tenant_patient patient
        left join app_tenant tenant
        on patient.tenant_code=tenant.code
        where patient.init_email = #{email}
          and patient.deleted=0
    </select>

    <select id="countTenantPatientByDoctors" resultType="java.lang.Integer">
        select count(DISTINCT tp.id) from app_tenant_patient tp
        LEFT JOIN app_tenant_doctor_patient tdp on tdp.patient_id = tp.id
        LEFT JOIN app_user u on tp.user_id = u.id
        where tp.tenant_code = #{tenantCode}
        and tp.`deleted` = 0
        and tdp.doctor_id in
        <foreach item="doctorId" index="index" collection="doctorIds" open="(" separator="," close=")">
            #{doctorId}
        </foreach>
        <include refid="listTenantPatient-if"></include>
    </select>

    <delete id="removePatient">
        delete from app_tenant_patient where id = #{patientId}
    </delete>

    <sql id="listTenantPatient-if">
        <if test="keyWord!=null">
            and (tp.nickname like concat('%', #{keyWord,jdbcType=VARCHAR}, '%')
            or tp.patient_number like concat('%', #{keyWord,jdbcType=VARCHAR}, '%')
            or u.email like concat('%',#{keyWord,jdbcType=VARCHAR}, '%')
            )
        </if>
    </sql>

</mapper>
