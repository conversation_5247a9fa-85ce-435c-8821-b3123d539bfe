package com.mira.mongo.domain;

import com.mira.api.bluetooth.enums.AlgorithmRequestTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 */
@Document("user_algorithm_request_log_2025")
@Getter
@Setter
public class UserAlgorithmRequestLog extends BaseDomain {
    @Id
    private String id;

    /**
     * 用户ID
     */
    @Indexed(name = "userId", direction = IndexDirection.DESCENDING)
    private Long userId;

    /**
     * 请求类型
     *
     * @see AlgorithmRequestTypeEnum
     */
    private Integer type;

    /**
     * 算法请求json
     */
    private Object request;

    /**
     * 错误信息
     */
    private String errorMsg;

}
