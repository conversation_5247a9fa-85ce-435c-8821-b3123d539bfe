package com.mira.script.dal.dao.app;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.script.DTO.ActiveUserInfoDO;
import com.mira.script.DTO.UserBindLogDO;
import com.mira.script.dal.entity.app.AppUserEntity;
import com.mira.script.dal.mapper.app.AppUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * app_user DAO
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class AppUserDAO extends ServiceImpl<AppUserMapper, AppUserEntity> {
    public List<ActiveUserInfoDO> listActiveUserInfoDOSBySql(String sql) {
        return this.baseMapper.listActiveUserInfoDOSBySql(sql);
    }

    @Slave
    public List<UserBindLogDO> listBindLogBySql(String sql) {
        return this.baseMapper.listBindLogBySql(sql);
    }
}
