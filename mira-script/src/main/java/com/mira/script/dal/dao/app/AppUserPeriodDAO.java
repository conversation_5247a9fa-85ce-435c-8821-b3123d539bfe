package com.mira.script.dal.dao.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mira.api.bluetooth.dto.period.UserPeriodDataDTO;
import com.mira.api.bluetooth.util.PeriodUtil;
import com.mira.core.util.StringListUtil;
import com.mira.script.dal.entity.app.AppUserPeriodEntity;
import com.mira.script.dal.mapper.app.AppUserPeriodMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * app_user_period DAO
 *
 * <AUTHOR>
 */
@Repository
public class AppUserPeriodDAO extends ServiceImpl<AppUserPeriodMapper, AppUserPeriodEntity> {
    public AppUserPeriodEntity getByUserId(Long userId) {
        return getOne(Wrappers.<AppUserPeriodEntity>lambdaQuery()
                .eq(AppUserPeriodEntity::getUserId, userId));
    }

    public List<AppUserPeriodEntity> listByUserIds(Set<Long> userIds) {
        QueryWrapper<AppUserPeriodEntity> infoWrapper = new QueryWrapper<>();
        infoWrapper.in("user_id", userIds);
        List<AppUserPeriodEntity> userPeriodEntities = this.list(infoWrapper);
        return userPeriodEntities;
    }

    public String getLastPeriodDate(Long userId, String timeZone) {
        AppUserPeriodEntity appUserPeriodEntity = this.getByUserId(userId);
        String periods = appUserPeriodEntity.getPeriods();
        if (StringUtils.isNotBlank(periods)) {
            List<Long> dbPeriodList = StringListUtil.strToLongList(periods, ",");
            // 以数据库的时间戳+用户当前时区来转换成用户本地时间
            List<String> periodStrList = PeriodUtil.periodsLong2String(dbPeriodList, timeZone);
            List<UserPeriodDataDTO> userPeriodDataDTOS = PeriodUtil.buildPeriodList(appUserPeriodEntity.getAvgLenPeriod(), periodStrList);
            UserPeriodDataDTO lastUserPeriodDataDTO = userPeriodDataDTOS.get(userPeriodDataDTOS.size() - 1);
            return lastUserPeriodDataDTO.getDate_period_start();
        }
        return null;
    }
}
