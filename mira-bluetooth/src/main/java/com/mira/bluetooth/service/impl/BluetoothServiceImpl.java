package com.mira.bluetooth.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mira.api.bluetooth.consts.ErrorAndWarningCodeConst;
import com.mira.api.bluetooth.dto.algorithm.AlgorithmResultDTO;
import com.mira.api.bluetooth.dto.cycle.CycleDataDTO;
import com.mira.api.bluetooth.dto.cycle.HormoneDTO;
import com.mira.api.bluetooth.dto.menopause.MenopauseResultDTO;
import com.mira.api.bluetooth.dto.wand.TestRemindDTO;
import com.mira.api.bluetooth.dto.wand.WandTestBiomarkerDTO;
import com.mira.api.bluetooth.dto.wand.WandsParamRecordDTO;
import com.mira.api.bluetooth.enums.RunBoardFlagEnum;
import com.mira.api.bluetooth.util.TestRemindUtil;
import com.mira.api.message.dto.PushNotificationDTO;
import com.mira.api.message.dto.PushTokenDTO;
import com.mira.api.message.enums.NotificationDefineEnum;
import com.mira.api.message.provider.IMessageProvider;
import com.mira.api.thirdparty.consts.KlaviyoPropertyConst;
import com.mira.api.thirdparty.enums.AmplitudeEventTypeEnum;
import com.mira.api.user.consts.RedisCacheKeyConst;
import com.mira.api.user.dto.user.SysTipsDTO;
import com.mira.api.user.dto.user.TemperatureDTO;
import com.mira.api.user.dto.user.UserBindDTO;
import com.mira.api.user.dto.user.UserBindVersionDTO;
import com.mira.api.user.enums.UserMenopauseStageEnum;
import com.mira.api.user.provider.IUserProvider;
import com.mira.bluetooth.async.AmplitudeProducer;
import com.mira.bluetooth.async.KlaviyoProducer;
import com.mira.bluetooth.consts.BluetoothDataConst;
import com.mira.bluetooth.consts.UpperValueConst;
import com.mira.bluetooth.controller.vo.DataHistoryVO;
import com.mira.bluetooth.controller.vo.DataUploadVO;
import com.mira.bluetooth.dal.dao.AppDataUploadDAO;
import com.mira.bluetooth.dal.dao.AppUserMenopauseResultDAO;
import com.mira.bluetooth.dal.entity.AppDataUploadEntity;
import com.mira.bluetooth.dal.entity.AppUserAlgorithmResultEntity;
import com.mira.bluetooth.dal.entity.AppUserMenopauseResultEntity;
import com.mira.bluetooth.dto.BluetoothDataDTO;
import com.mira.bluetooth.dto.DataUploadDTO;
import com.mira.bluetooth.dto.algorithm.request.NewHormoneDataRequest;
import com.mira.bluetooth.dto.algorithm.response.AlgorithmReturnDTO;
import com.mira.bluetooth.enums.DataSourceEnum;
import com.mira.bluetooth.enums.HormoneWarningEnum;
import com.mira.api.bluetooth.enums.BarTipEnum;
import com.mira.bluetooth.handler.event.dto.NewHormoneEventDTO;
import com.mira.bluetooth.handler.event.upload.KlaviyoSyncEvent;
import com.mira.bluetooth.handler.event.upload.NewHormoneEvent;
import com.mira.bluetooth.handler.event.upload.NewHormoneWarningEvent;
import com.mira.bluetooth.service.IBluetoothService;
import com.mira.bluetooth.service.manager.AlgorithmInvokeManager;
import com.mira.bluetooth.service.manager.AlgorithmResultManager;
import com.mira.bluetooth.service.manager.BluetoothManager;
import com.mira.bluetooth.service.manager.CacheManager;
import com.mira.bluetooth.service.util.*;
import com.mira.core.consts.DatePatternConst;
import com.mira.core.consts.HeaderConst;
import com.mira.core.consts.enums.WandTypeEnum;
import com.mira.core.holder.BaseLoginInfo;
import com.mira.core.holder.ContextHolder;
import com.mira.core.response.CommonResult;
import com.mira.core.util.*;
import com.mira.mybatis.util.UpdateEntityTimeUtil;
import com.mira.web.properties.SysDictProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 蓝牙数据接口实现
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class BluetoothServiceImpl implements IBluetoothService {
    private final AppDataUploadDAO appDataUploadDAO;
    private final DailyWandTestDataHelper dailyWandTestDataHelper;
    private final RunBoardCheckDataHelper runBoardCheckDataHelper;
    private final WandsParamRecordHelper wandsParamRecordHelper;
    private final BBTBiomarkerHelper bbtBiomarkerHelper;
    private final CacheManager cacheManager;
    private final BluetoothManager bluetoothManager;
    private final AlgorithmInvokeManager algorithmInvokeManager;
    private final AlgorithmResultManager algorithmResultManager;
    private final SysDictProperties sysDictProperties;
    private final IUserProvider userProvider;
    private final AppUserMenopauseResultDAO userMenopauseResultDAO;
    private final IMessageProvider messageProvider;
    private final KlaviyoProducer klaviyoProducer;
    private final AmplitudeProducer amplitudeProducer;

    @Override
    public DataUploadVO upload(DataUploadDTO dataUploadDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        String email = loginInfo.getUsername();
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);

        // 返回的蓝牙数据
        List<BluetoothDataDTO> returnBluetoothDatas = new ArrayList<>();
        // 激素列表
        List<HormoneDTO> hormoneList = new ArrayList<>();
        // 设备序列号
        String currentSn = null;
        // 获取trial标记
        Integer trialFlag = getTrialFlag(userId);
        // 获取用户所有数据的时间
        List<Long> completeTimestampList = appDataUploadDAO.getCompleteTimestampByUserId(userId);
        // 待新增的测试数据
        List<AppDataUploadEntity> newUploadDataList = new ArrayList<>();
        // 测试试剂类型列表
        Set<String> testWandTypeSet = new HashSet<>();

        log.info("start upload data, data size:{}", dataUploadDTO.getBluetoothDatas().size());
        for (BluetoothDataDTO inBluetoothData : dataUploadDTO.getBluetoothDatas()) {
            AppDataUploadEntity dataUploadEntity = parseDataEntity(inBluetoothData, userId, timeZone, returnBluetoothDatas);
            if (dataUploadEntity == null) {
                continue;
            }
            if (checkDuplicatesAndSnLimit(userId, inBluetoothData, dataUploadEntity, completeTimestampList, returnBluetoothDatas)) {
                continue;
            }
            String testWandType = dataUploadEntity.getTestWandType();
            WandsParamRecordDTO wandsParamRecordDTO = wandsParamRecordHelper.getByWandBatch3(dataUploadEntity.getTestWandBatch(),
                    testWandType.startsWith("0") ? testWandType.substring(1) : testWandType);
            setRunBoardFlag(dataUploadEntity, wandsParamRecordDTO);
            // add new
            newUploadDataList.add(dataUploadEntity);
            // 检查支持的试剂类型,例如FF的直接过滤
            if (StringUtils.isBlank(testWandType) || !WandTypeEnum.getAllowWandTypes().contains(testWandType)) {
                continue;
            }
            // add wand type
            testWandTypeSet.add(testWandType);
            // build return data
            BluetoothDataDTO returnBluetoothData = new BluetoothDataDTO();
            returnBluetoothData.setCompleteTimeStamp(inBluetoothData.getCompleteTimeStamp());
            returnBluetoothData.setStatus(BluetoothDataConst.DATA_UPLOAD_SUCCESS);
            returnBluetoothData.setBluetoothData(inBluetoothData.getBluetoothData());
            returnBluetoothDatas.add(returnBluetoothData);
        }
        // save data
        if (CollectionUtils.isNotEmpty(newUploadDataList)) {
            // 去重
            removeDuplicate(newUploadDataList);
            saveData(newUploadDataList);
            if (testWandTypeSet.contains(WandTypeEnum.LH_E3G_PDG.getString())) {
                CompletableFuture.runAsync(() -> userProvider.wandChange(userId, WandTypeEnum.LH_E3G_PDG.getString()),
                        ThreadPoolUtil.getPool());
            }
        }
        // 构建激素信息
        for (AppDataUploadEntity dataUploadEntity : newUploadDataList) {
            String testWandType = dataUploadEntity.getTestWandType();
            WandsParamRecordDTO wandsParamRecordDTO = wandsParamRecordHelper.getByWandBatch3(dataUploadEntity.getTestWandBatch(),
                    testWandType.startsWith("0") ? testWandType.substring(1) : testWandType);
            HormoneDTO hormoneDTO = bluetoothManager.convertToHormoneDTO(dataUploadEntity, wandsParamRecordDTO, trialFlag);
            if (Objects.nonNull(hormoneDTO)) {
                hormoneList.add(hormoneDTO);
                // 处理新数据上传后的告警逻辑
                if (Objects.isNull(trialFlag) || !trialFlag.equals(WandTypeEnum.HCG_QUALITATIVE.getInteger())) {
                    Integer wandType = hormoneDTO.getTest_results().getWand_type();
                    if (WandTypeEnum.HCG_QUALITATIVE.getInteger().equals(wandType)) {
                        SpringContextHolder.publishEvent(new NewHormoneWarningEvent(userId, timeZone, HormoneWarningEnum.HCG_QUALITATIVE.getWarningNo()));
                    }
                }
                currentSn = dataUploadEntity.getSn();
            }
        }
        // check qc user
        try {
            if (sysDictProperties.getQcEmails().contains(email)) {
                log.info("qc user:{} upload data", email);
                DataUploadVO dataUploadVO = new DataUploadVO();
                dataUploadVO.setBluetoothDatas(returnBluetoothDatas);
                return dataUploadVO;
            }
        } catch (Exception e) {
            log.error("check qc user:{} error", email);
        }
        // return
        return buildDataUploadVO(userId, trialFlag, currentSn, hormoneList, returnBluetoothDatas,
                dataUploadDTO.getTipsType());
    }

    private void removeDuplicate(List<AppDataUploadEntity> newUploadDataList) {
        Set<String> uniqueIdentity = new HashSet<>();
        Iterator<AppDataUploadEntity> iterator = newUploadDataList.iterator();
        while (iterator.hasNext()) {
            AppDataUploadEntity dataUploadEntity = iterator.next();
            String identity = dataUploadEntity.getUserId() + "_" + dataUploadEntity.getCompleteTime();
            if (!uniqueIdentity.add(identity)) {
                iterator.remove();
            }
        }
    }

    private void saveData(List<AppDataUploadEntity> newUploadDataList) {
        try {
            appDataUploadDAO.saveBatch(newUploadDataList);
        } catch (Exception e1) {
            // 批量存储失败，逐条存储
            Iterator<AppDataUploadEntity> iterator = newUploadDataList.iterator();
            while (iterator.hasNext()) {
                AppDataUploadEntity dataUploadEntity = iterator.next();
                try {
                    appDataUploadDAO.save(dataUploadEntity);
                } catch (Exception e2) {
                    iterator.remove();
                    log.error("user:{}, data:{} save error", dataUploadEntity.getUserId(), JsonUtil.toJson(dataUploadEntity));
                }
            }
        }
    }

    private void setRunBoardFlag(AppDataUploadEntity dataUploadEntity, WandsParamRecordDTO wandsParamRecordDTO) {
        String error = dataUploadEntity.getError();
        String warning = dataUploadEntity.getWarning();
        // 前端不显示的数据
        if (!ErrorAndWarningCodeConst.MEANINGFUL_ERROR_CODE.contains(error)
                || !ErrorAndWarningCodeConst.MEANINGFUL_WARN_CODE.contains(warning)) {
            log.warn("setRunBoardFlag data error, userId:{}, error:{}, warning:{}", dataUploadEntity.getUserId(), error, warning);
            dataUploadEntity.setRunBoardFlag(RunBoardFlagEnum.ERROR_DATA.getCode());
            // 前端显示异常的数据
        } else if (ErrorAndWarningCodeConst.FRONT_DISPLAY_ERROR_CODE.contains(error)
                || ErrorAndWarningCodeConst.FRONT_DISPLAY_WARN_CODE.contains(warning)) {
            log.warn("setRunBoardFlag data error, userId:{}, error:{}, warning:{}", dataUploadEntity.getUserId(), error, warning);
            dataUploadEntity.setRunBoardFlag(RunBoardFlagEnum.ERROR_DATA.getCode());
        } else {
            boolean checkDataBool = runBoardCheckDataHelper.checkData(dataUploadEntity, wandsParamRecordDTO);
            if (checkDataBool) {
                dataUploadEntity.setRunBoardFlag(RunBoardFlagEnum.PASS.getCode());
            } else {
                dataUploadEntity.setRunBoardFlag(RunBoardFlagEnum.FAIL.getCode());
            }
        }
    }

    @Nullable
    private Integer getTrialFlag(Long userId) {
        Integer trialFlag;
        try {
            CommonResult<WandTypeEnum> userProductTrialResult = userProvider.getUserProductTrial(userId);
            trialFlag = userProductTrialResult.getData().getInteger();
        } catch (Exception e) {
            trialFlag = null;
        }
        return trialFlag;
    }

    @Nullable
    private static AppDataUploadEntity parseDataEntity(BluetoothDataDTO inBluetoothData, Long userId, String timeZone,
                                                       List<BluetoothDataDTO> returnBluetoothDatas) {
        // 解析蓝牙数据
        AppDataUploadEntity dataUploadEntity;
        try {
            dataUploadEntity = BluetoothDataUtil.parseDataEntity(inBluetoothData, userId, timeZone);
            UpdateEntityTimeUtil.setBaseEntityTime(timeZone, dataUploadEntity);
        } catch (Exception ex) {
            log.info("user:{}，analysis bluetooth data occur error:{}", userId, ex);
            BluetoothDataDTO returnBluetoothData = new BluetoothDataDTO();
            returnBluetoothData.setCompleteTimeStamp(inBluetoothData.getCompleteTimeStamp());
            returnBluetoothData.setStatus(BluetoothDataConst.DATA_UPLOAD_PARSE_ERROR);
            returnBluetoothDatas.add(returnBluetoothData);
            return null;
        }
        // 设置程序支持的上限
        setUpperValue(dataUploadEntity);
        // 数据来源
        dataUploadEntity.setSource(DataSourceEnum.VERSION4.getSource());
        return dataUploadEntity;
    }

    /**
     * 检查数据是否重复，以及每日Sn测试数量限制
     *
     * @return true表示命中规则，需要跳出继续执行的逻辑
     */
    private boolean checkDuplicatesAndSnLimit(Long userId,
                                              BluetoothDataDTO inBluetoothData,
                                              AppDataUploadEntity dataUploadEntity,
                                              List<Long> completeTimestampList,
                                              List<BluetoothDataDTO> returnBluetoothDatas) {
        // 检查重复数据
        if (completeTimestampList.contains(dataUploadEntity.getCompleteTimestamp())) {
            log.info("userId:{} bluetooth data already exist", userId);
            BluetoothDataDTO returnBluetoothData = new BluetoothDataDTO();
            returnBluetoothData.setCompleteTimeStamp(inBluetoothData.getCompleteTimeStamp());
            returnBluetoothData.setStatus(BluetoothDataConst.DATA_UPLOAD_ALREADY_EXIST);
            returnBluetoothDatas.add(returnBluetoothData);
            return true;
        }
        // 白名单
        Boolean isWhiteUser = Boolean.FALSE;
        try {
            isWhiteUser = cacheManager.isInDataUploadWhiteList(userId);
        } catch (Exception e) {
            log.error("DATA_UPLOAD_WHITE key not found, please check.");
        }
        // 黑名单，一定时间内限制测试次数
        Boolean snLimit = cacheManager.isRestrictDevice(dataUploadEntity.getSn());
        if (snLimit && !isWhiteUser) {
            dataUploadEntity.setRunBoardFlag(RunBoardFlagEnum.DELETED.getCode());
            dataUploadEntity.setDeleted(1);
            appDataUploadDAO.save(dataUploadEntity);

            BluetoothDataDTO returnBluetoothData = new BluetoothDataDTO();
            returnBluetoothData.setCompleteTimeStamp(inBluetoothData.getCompleteTimeStamp());
            returnBluetoothData.setStatus(BluetoothDataConst.DATA_UPLOAD_ALREADY_EXIST);
            returnBluetoothDatas.add(returnBluetoothData);
            return true;
        }
        return false;
    }

    private static void setUpperValue(AppDataUploadEntity dataUploadEntity) {
        if (dataUploadEntity.getTestWandType().equals("0" + WandTypeEnum.LH.getString())) {
            if (UpperValueConst.LH_UPPER_VALUE.compareTo(dataUploadEntity.getT1ConValue()) < 0) {
                dataUploadEntity.setT1ConValue(UpperValueConst.LH_UPPER_VALUE);
            }
        } else if (dataUploadEntity.getTestWandType().equals("0" + WandTypeEnum.E3G_LH.getString())) {
            if (UpperValueConst.LH_UPPER_VALUE.compareTo(dataUploadEntity.getT2ConValue()) < 0) {
                dataUploadEntity.setT2ConValue(UpperValueConst.LH_UPPER_VALUE);
            }
            if (UpperValueConst.E3G_UPPER_VALUE.compareTo(dataUploadEntity.getT1ConValue()) < 0) {
                dataUploadEntity.setT1ConValue(UpperValueConst.E3G_UPPER_VALUE);
            }
        } else if (dataUploadEntity.getTestWandType().equals("0" + WandTypeEnum.HCG.getString())) {
            if (UpperValueConst.HCG_UPPER_VALUE.compareTo(dataUploadEntity.getT3ConValue()) < 0) {
                dataUploadEntity.setT3ConValue(UpperValueConst.HCG_UPPER_VALUE);
            }
        }
    }

    private DataUploadVO buildDataUploadVO(Long userId, Integer trialFlag, String currentSn,
                                           List<HormoneDTO> hormoneList, List<BluetoothDataDTO> returnBluetoothDatas,
                                           Integer tipsType) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        DataUploadVO resultVO = new DataUploadVO();
        if (CollectionUtils.isNotEmpty(hormoneList)) {
            String today = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
            // 调用算法
            NewHormoneDataRequest newHormoneDataRequest = algorithmInvokeManager.buildNewHormoneRequest(userId, today, trialFlag, hormoneList);
            AlgorithmReturnDTO algorithmReturnDataDTO = algorithmInvokeManager.callNewHormoneData(newHormoneDataRequest);
            // 更新算法结果
            AppUserAlgorithmResultEntity userAlgorithmResultEntity = updateAlgorithResult(newHormoneDataRequest, algorithmReturnDataDTO);
            // Tips
            boolean isNewTips = (tipsType != null) && (tipsType == 1);
            CompletableFuture<SysTipsDTO> tipsAsyncFuture = isNewTips ? getTipsAsync(userId) : null;
            // HCG
            List<Long> hideHcgValueUserIds = sysDictProperties.getHideHcgValueUserIds();
            if (hideHcgValueUserIds != null && hideHcgValueUserIds.contains(userId)) {
                // do nothing
            } else {
                // TODO hcg怀孕相关逻辑暂时已被注释
                // SpringContextHolder.publishEvent(new HandlePregnantEvent(newHormoneDataRequest, userAlgorithmResultEntity));
                // 同步Klaviyo
                SpringContextHolder.publishEvent(new KlaviyoSyncEvent(userId, newHormoneDataRequest, algorithmReturnDataDTO));
                // 处理新数据上传后的其他逻辑
                SpringContextHolder.publishEvent(new NewHormoneEvent(new NewHormoneEventDTO()
                        .setUserId(userId)
                        .setNewHormoneDataRequest(newHormoneDataRequest)
                        .setAlgorithmReturnData(algorithmReturnDataDTO)
                        .setCurrentSn(currentSn)
                        .setTestCount(hormoneList.size())));
            }

            DataHistoryVO dataHistoryVO = buildDataHistoryVO(userId, today, BeanUtil.toBean(userAlgorithmResultEntity, AlgorithmResultDTO.class));
            List<String> testTimes = new ArrayList<>();
            returnBluetoothDatas.stream()
                                .filter(returnBluetoothData -> returnBluetoothData.getStatus() == 1)
                                .map(BluetoothDataDTO::getBluetoothData)
                                .forEach(rawResultData -> {
                                    long completeTime = Long.parseLong(rawResultData.substring(56, 64), 16) * 1000;
                                    String testTime = ZoneDateUtil.format(timeZone, completeTime, DatePatternConst.DATE_TIME_PATTERN);
                                    testTimes.add(testTime);
                                });
            List<WandTestBiomarkerDTO> wandTestDataList = dataHistoryVO.getWandTestDataList();
            List<WandTestBiomarkerDTO> filterWandTestDataList =
                    wandTestDataList.stream()
                                    .filter(wandTestData -> testTimes.contains(wandTestData.getTestTime()))
                                    .collect(Collectors.toList());
            dataHistoryVO.setWandTestDataList(filterWandTestDataList);
            resultVO.setDataHistoryDTO(dataHistoryVO);
            if (isNewTips && tipsAsyncFuture != null) {
                setTips(resultVO, hormoneList, filterWandTestDataList, tipsAsyncFuture);
            }
        }
        log.info("returnBluetoothDatas.size:{}", returnBluetoothDatas.size());

        resultVO.setBluetoothDatas(returnBluetoothDatas);
        return resultVO;
    }

    /**
     * 更新算法结果
     */
    private AppUserAlgorithmResultEntity updateAlgorithResult(NewHormoneDataRequest newHormoneDataRequest, AlgorithmReturnDTO algorithmReturnDataDTO) {
        Long userId = newHormoneDataRequest.getUser_id();
        String timeZone = newHormoneDataRequest.getTimeZone();
        List<HormoneDTO> hormoneDatas = new ArrayList<>();
        List<HormoneDTO> hormoneDataHistory = newHormoneDataRequest.getHormone_data_history();
        List<HormoneDTO> hormoneDataNew = newHormoneDataRequest.getHormone_data_new();

        if (CollectionUtils.isNotEmpty(hormoneDataHistory)) {
            hormoneDatas.addAll(hormoneDataHistory);
        }
        if (CollectionUtils.isNotEmpty((hormoneDataNew))) {
            hormoneDatas.addAll(hormoneDataNew);
        }

        // update algorithm
        AppUserAlgorithmResultEntity userAlgorithmResultEntity = algorithmResultManager
                .saveOrUpdateAlgorithmResult(userId, timeZone, hormoneDatas, algorithmReturnDataDTO);
        cacheManager.cacheCycleDataUpdate(userId, newHormoneDataRequest, algorithmReturnDataDTO);

        // update cache
        cacheManager.cacheAlgorithmResult(userId, userAlgorithmResultEntity);
        // delete cache
        cacheManager.deleteTipsAlgorithmResult(userId);
        Integer trackingMenopause = newHormoneDataRequest.getTracking_menopause();
        if (trackingMenopause != null && trackingMenopause == 1) {
            CompletableFuture.runAsync(() -> {
                processMenopauseResult(userId, algorithmReturnDataDTO, timeZone);
            }, ThreadPoolUtil.getPool()).exceptionally(ex -> {
                log.error("processMenopauseResult error", ex);
                return null;
            });
        }

        return userAlgorithmResultEntity;
    }

    private void processMenopauseResult(Long userId, AlgorithmReturnDTO algorithmReturnDTO, String timeZone) {
        Integer trackingMenopauseBackendStatus = algorithmReturnDTO.getTracking_menopause_backend_status();
        Float trackingMenopauseProgressStatus = algorithmReturnDTO.getTracking_menopause_progress_status();

        AppUserMenopauseResultEntity userMenopauseResultEntity = userMenopauseResultDAO.getByUserId(userId);
        if (userMenopauseResultEntity == null) {
            return;
        }
        //send the notification right after the stage percent became 60% in (menopause define stage:  0 && backend
        // status:1 )
        // 正好完成第一个周期
        Long notificationDefineId = NotificationDefineEnum.MENOPAUSE_ONE_CYCLE_DONE.getDefineId();
        Float progressStatus = userMenopauseResultEntity.getProgressStatus();
        Integer defineStage = userMenopauseResultEntity.getDefineStage();
        log.info("userId:【{}】,progressStatus:【{}】,trackingMenopauseProgressStatus:【{}】,trackingMenopauseBackendStatus:【{}】",
                userId, progressStatus, trackingMenopauseProgressStatus, trackingMenopauseBackendStatus);
        if (!Objects.equals(progressStatus, 0.6f) && Objects.equals(trackingMenopauseProgressStatus, 0.6f)) {
            PushTokenDTO pushToken = cacheManager.getPushToken(userId);
            if (StringUtils.isBlank(pushToken.getPushToken())) {
                log.error("define:{}, user:{} push token is empty", notificationDefineId, userId);
                return;
            }
            log.info("send menopause notification MENOPAUSE_ONE_CYCLE_DONE 304, userId:【{}】,",
                    userId);
            messageProvider.sendNotification(new PushNotificationDTO()
                    .setUserId(userId)
                    .setTimeZone(timeZone)
                    .setPlatform(pushToken.getPlatform())
                    .setDefineId(notificationDefineId)
                    .setPushFirebase(Boolean.TRUE)
                    .setSaveRecord(Boolean.TRUE)
                    .setExpireTime(-1L)
                    .setTokens(Collections.singletonList(pushToken.getPushToken())));

            // Klaviyo
            String dateTime = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
            klaviyoProducer.addMenopauseProperty(userId, KlaviyoPropertyConst.MENOPAUSE_FINISH_FIRST_CYCLE, dateTime);
            // amplitude
            amplitudeProducer.amplitudeEventLog(userId, AmplitudeEventTypeEnum.MENOPAUSE_FINISH_FIRST_CYCLE, dateTime);
        } else if (!Objects.equals(progressStatus, 1f) && Objects.equals(trackingMenopauseProgressStatus, 1f)) {
            // 正好完成第二个周期
            // Klaviyo
            String dateTime = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_TIME_PATTERN);
            klaviyoProducer.addMenopauseProperty(userId, KlaviyoPropertyConst.MENOPAUSE_FINISH_SECOND_CYCLE, dateTime);
            // amplitude
            amplitudeProducer.amplitudeEventLog(userId, AmplitudeEventTypeEnum.MENOPAUSE_FINISH_SECOND_CYCLE, dateTime);
        }
        if (progressStatus != null && progressStatus >= 0.6f) {//测完第一轮之后
            if (defineStage != null) {
                UserMenopauseStageEnum menopauseStageEnum = UserMenopauseStageEnum.get(defineStage);
                klaviyoProducer.addMenopauseProperty(userId, KlaviyoPropertyConst.MENOPAUSE_STAGE,
                        menopauseStageEnum.getDesc());
            }
        }

        cacheManager.deleteMenopauseResult(userId);
    }


    /**
     * 构建当日的测试数据历史
     */
    private DataHistoryVO buildDataHistoryVO(Long userId, String dateStr, AlgorithmResultDTO algorithmResultDTO) {
        DataHistoryVO dataHistoryDTO = new DataHistoryVO();
        List<TemperatureDTO> temperatureList = userProvider.getTemperatureList(userId, dateStr).getData();
        List<TemperatureDTO> filterTemperatureDTOList = temperatureList.stream()
                                                                       .filter(temperatureDTO -> temperatureDTO.getType() != 0)
                                                                       .collect(Collectors.toList());

        Integer barTip = algorithmResultDTO.getBarTip();
        if (BarTipEnum.HCG_TEST.getCode().equals(barTip)) {
            barTip = null;
        }
        dataHistoryDTO.setBarTip(barTip);

        List<CycleDataDTO> cycleDataDTOList = JsonUtil.toArray(algorithmResultDTO.getCycleData(), CycleDataDTO.class);
        List<HormoneDTO> hormoneDataList = JsonUtil.toArray(algorithmResultDTO.getHormoneData(), HormoneDTO.class);
        List<HormoneDTO> dailyHormoneDataList =
                hormoneDataList.stream()
                               .filter(hormoneData
                                       -> dateStr.equals(LocalDateUtil.format(hormoneData.getTest_time(), DatePatternConst.DATE_TIME_PATTERN, DatePatternConst.DATE_PATTERN)))
                               .filter(hormoneDTO
                                       -> StringUtils.isBlank(hormoneDTO.getTest_results().getEcode()) || !hormoneDTO.getTest_results().getEcode().startsWith("EB"))
                               .collect(Collectors.toList());
        List<WandTestBiomarkerDTO> wandTestBiomarkerDTOList =
                dailyWandTestDataHelper.getDailyWandTestData(dailyHormoneDataList, userId);

        long count = hormoneDataList.stream()
                                    .filter(hormoneDTO -> hormoneDTO.getFlag() == 1).count();
        if (count != 0) {
            dataHistoryDTO.setNumOfTest(Integer.valueOf((int) count));
        }
        for (TemperatureDTO temperatureDTO : filterTemperatureDTOList) {
            WandTestBiomarkerDTO wandTestBiomarkerDTO = bbtBiomarkerHelper.buildBBTBiomarkerDTO(userId, temperatureDTO);
            wandTestBiomarkerDTOList.add(wandTestBiomarkerDTO);
        }
        dataHistoryDTO.setWandTestDataList(wandTestBiomarkerDTOList);

        List<TestRemindDTO> testingDTOList = TestRemindUtil.getTestingProducts(dateStr, cycleDataDTOList, wandTestBiomarkerDTOList);
        dataHistoryDTO.setTestingDTOS(testingDTOList);
        return dataHistoryDTO;
    }

    /**
     * 获取tips
     */
    private CompletableFuture<SysTipsDTO> getTipsAsync(Long userId) {
        return CompletableFuture.supplyAsync(() -> userProvider.buildTipsResult(userId).getData(), ThreadPoolUtil.getPool())
                                .exceptionally(ex -> {
                                    log.error("get tips error", ex);
                                    return null;
                                });
    }

    /**
     * 设置tips
     */
    private void setTips(DataUploadVO resultVO, List<HormoneDTO> hormoneList,
                         List<WandTestBiomarkerDTO> filterWandTestDataList,
                         CompletableFuture<SysTipsDTO> tipsAsyncFuture) {
        String timeZone = ContextHolder.get(HeaderConst.TIME_ZONE);
        try {
            // 本次数据上传包含的试纸类型
            List<Integer> wandTypeList = hormoneList.stream()
                                                    .map(hormoneDTO -> hormoneDTO.getTest_results().getWand_type())
                                                    .collect(Collectors.toList());
            // 试纸类型对应的试剂类型列表，biomarker
            List<WandTypeEnum> wandTypeEnumList = wandTypeList.stream()
                                                              .map(WandTypeEnum::get).collect(Collectors.toList());
            Set<Integer> biomarkerSet = new HashSet<>();
            wandTypeEnumList.forEach(wandType -> biomarkerSet.addAll(wandType.getBiomarker()));
            SysTipsDTO sysTipsDTO = tipsAsyncFuture.get(15, TimeUnit.SECONDS);
            // 筛选tips
            if (sysTipsDTO == null) {
                return;
            }
            sysTipsDTO.setTips(sysTipsDTO.getTips().stream()
                                         .filter(tip -> biomarkerSet.contains(tip.getBiomarker())).collect(Collectors.toList()));

            // 上传的数据中，今天的数据只要有一条（没有Ecode）的数据，tips type = 1，否则 0
            Predicate<List<WandTestBiomarkerDTO>> tipsTypePredicate = wandTestList -> {
                String nowDate = ZoneDateUtil.format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
                // 当天
                return wandTestList.stream()
                                   .filter(data -> {
                                       // 没有错误的数据
                                       String ecode = data.getEcode();
                                       return ecode == null || ecode.indexOf("B") == 0;
                                   }).anyMatch(data -> {
                            // 当天
                            int minusToDay = LocalDateUtil.minusToDay(data.getTestTime(), nowDate);
                            return minusToDay == 0;
                        });
            };

            if (tipsTypePredicate.test(filterWandTestDataList)) {
                sysTipsDTO.setType(1);
            } else {
                sysTipsDTO.setType(0);
            }

            resultVO.setTips(sysTipsDTO);
        } catch (Exception e) {
            log.error("getTipsAsync error", e);
        }
    }

    @Override
    public Long getUserMaxCompleteTime() {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        return appDataUploadDAO.getUserMaxCompleteTime(userId);
    }

    @Override
    public DataHistoryVO getHistory(String dateStr) {
        Long userId = ContextHolder.<BaseLoginInfo>getLoginInfo().getId();
        return buildDataHistoryVO(userId, dateStr, cacheManager.getAlgorithmResultCache(userId));
    }

    @Override
    public Integer bind(UserBindDTO userBindDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        // 绑定或解绑
        CommonResult<Integer> result = userProvider.bindUnbindDevice(userId, userBindDTO);

        return result.getData();
    }

    @Override
    public void editBindVersion(UserBindVersionDTO userBindVersionDTO) {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        // 修改绑定版本号
        userProvider.editBindVersion(userId, userBindVersionDTO);
    }

    @Override
    public void waitShipping() {
        BaseLoginInfo loginInfo = ContextHolder.getLoginInfo();
        Long userId = loginInfo.getId();
        userProvider.waitShipping(userId);
    }
}
