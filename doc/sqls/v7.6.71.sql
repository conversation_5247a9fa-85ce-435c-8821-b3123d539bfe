alter table app_tenant
    add type tinyint default 0 not null comment '类型：0:clinic;1:doctor' after name;

alter table app_tenant_doctor
    add doctor_type tinyint default 0 not null comment '类型：0:clinic 管理员或者 单独的doctor ;1:clinic的医生或者护士; 默认0' after role;

update app_tenant_doctor set doctor_type=1 where role in (2,3);

create table app_tenant_hipaa
(
    id              bigint auto_increment comment '主键'
        primary key,
    tenant_code     varchar(50)       not null comment '诊所平台code',
    doctor_id       bigint            not null  comment '医生id',
    agree           tinyint           null comment '是否同意hippa安全协议：0:ignore;1:agree; null表示未处理',
    creator         bigint  default 0 not null comment '创建者ID 默认0',
    modifier        bigint  default 0 not null comment '修改者ID 默认0',
    create_time     bigint            null comment '创建时间',
    modify_time     bigint            null comment '修改时间',
    create_time_str varchar(255)      null comment '创建时间',
    modify_time_str varchar(255)      null comment '修改时间',
    time_zone       varchar(100)      not null comment '时区',
    deleted         tinyint default 0 not null comment '是否删除 0否,1是 (默认0)',
    constraint doctor_id
        unique (doctor_id)
)
    comment '诊所平台同意hipaa隐私协议情况表';

alter table app_data_upload
    modify t1_peak_area decimal(10, 2) null comment 't1线峰面积';
alter table app_data_upload
    modify t2_peak_area decimal(10, 2) null comment 't2线峰面积';
alter table app_data_upload
    modify t3_peak_area decimal(10, 2) null comment 't3线峰面积';
alter table app_data_upload
    modify t4_peak_area decimal(10, 2) null comment 't4线峰面积';


alter table app_data_manual
    add double_check tinyint default 0 not null comment '是否被处理人检查：0未检查；1已检查';