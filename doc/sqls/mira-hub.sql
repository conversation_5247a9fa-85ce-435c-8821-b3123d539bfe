use mira_hub;

-- 1--------------
drop table if exists mira_hub.hub_user;
create table if not exists mira_hub.hub_user
(
    id             bigint auto_increment comment 'id'
        primary key,
    hub_id         varchar(32)  not null,
    current_ip     varchar(255) null,
    country_code   varchar(20)  null,
    continent_code varchar(20)  null,
    create_time    bigint       not null,
    modify_time    bigint       not null,
    time_zone      varchar(100) not null,
    sync_time      bigint       not null comment 'data synchronized time'
)
    comment 'hub user table' charset = utf8;

create unique index idx_hub_id
    on hub_user (hub_id);

-- 2--------------
drop table if exists mira_hub.hub_user_info;
create table if not exists mira_hub.hub_user_info
(
    id           bigint auto_increment comment 'id'
        primary key,
    hub_id       varchar(32) not null,
    birth_year   int         null,
    birth_month  int         null,
    birth_of_day int         null,
    goal_status  int         null comment 'user mode:0:Cycle tracking;1:TTA-Trying to Avoid Pregnancy;2:TTC-Trying to Conceive;3:Pregnancy tracking',
    conditions   varchar(25) null comment 'user Condition enum:0:Hormone imbalance;1:Irregular cycle;2:PCOS;3:Miscarriage history;4:Just came off from birth control;5:Breastfeeding;6:None',
    modify_time  bigint      not null,
    sync_time    bigint      not null comment 'data synchronized time'
)
    comment 'hub user info' charset = utf8;

create unique index idx_hub_id
    on hub_user_info (hub_id);

-- 3--------------
drop table if exists mira_hub.hub_user_period;
create table if not exists mira_hub.hub_user_period
(
    id             bigint auto_increment comment 'id'
        primary key,
    hub_id         varchar(32)       not null,
    periods        longtext          null comment 'user manual periods',
    period_data    longtext          null comment 'Calculated period information',
    avg_len_period int               null comment 'average period length',
    avg_len_cycle  int               null comment 'average cycle length',
    period_flag    tinyint default 0 not null comment 'period flag：0：normal；-1：I don''t know',
    cycle_flag     tinyint default 0 not null comment 'cycle flag：0：normal；-1：I don''t know;-2 varies often ；-3  I don''t know + varies often',
    modify_time    bigint            not null,
    sync_time      bigint            not null comment 'data synchronized time'
)
    comment 'hub user period info' charset = utf8;

create unique index idx_hub_id
    on hub_user_period (hub_id);

-- 4--------------
drop table if exists mira_hub.hub_user_bbt;
create table if not exists mira_hub.hub_user_bbt
(
    id          bigint auto_increment comment 'id'
        primary key,
    hub_id      varchar(32)       not null,
    test_date   varchar(50)       not null comment 'test date',
    test_time   varchar(255)      not null comment 'test time',
    value       decimal(19, 3)    null comment 'temperature value with unit f',
    type        tinyint default 0 not null comment '0:default，1:from thermometers product 1',
    modify_time bigint            not null,
    sync_time   bigint            not null comment 'data synchronized time'
)
    comment 'hub user bbt data';

create index idx_hub_id
    on hub_user_bbt (hub_id);

-- 5--------------
drop table if exists mira_hub.hub_user_hormone;
create table if not exists mira_hub.hub_user_hormone
(
    id             bigint auto_increment comment 'id'
        primary key,
    data_id        bigint            not null,
    hub_id         varchar(32)       not null,
    sn             varchar(255)      null comment 'sn',
    complete_time  varchar(255)      null comment 'wands test complete time',
    test_wand_type varchar(32)       null comment 'wand type',
    biomarker      int               null comment 'wand biomarker,1:LH ; 3:E3G ; 9:PdG 2:HCG ;16:FSH',
    con_value      decimal(8, 2)     null comment 'concentration value',
    auto_flag      tinyint default 0 not null comment 'data type:0:auto;1manual(default0)',
    modify_time    bigint            not null,
    sync_time      bigint            not null comment 'data synchronized time'
)
    comment 'hub user bbt data';

create index idx_hub_id
    on hub_user_hormone (hub_id);

-- 6--------------
drop table if exists mira_hub.hub_user_os;
create table if not exists mira_hub.hub_user_os
(
    id           bigint auto_increment comment 'id'
        primary key,
    hub_id       varchar(32)  not null,
    os           varchar(128) null comment 'iOS/android',
    version      varchar(255) null comment 'phone system version',
    device       varchar(255) null comment 'phone device',
    app_version  varchar(255) null comment 'app version',
    network_type varchar(255) null comment 'network type',
    modify_time  bigint       not null,
    sync_time    bigint       not null comment 'data synchronized time'
)
    comment 'hub user recent access log';

create unique index idx_hub_id
    on hub_user_os (hub_id);

-- 7--------------
drop table if exists mira_hub.hub_user_bind;
create table if not exists mira_hub.hub_user_bind
(
    id           bigint auto_increment comment 'id'
        primary key,
    hub_id       varchar(32)  not null,
    sn           varchar(255) null comment 'sn',
    bind_time    varchar(255) null comment 'bind time',
    bind_version varchar(255) null comment 'bind firmware version',
    modify_time  bigint       not null,
    sync_time    bigint       not null comment 'data synchronized time'
)
    comment 'hub user tracker current bind status';

create unique index idx_hub_id
    on hub_user_bind (hub_id);

-- 8--------------
drop table if exists mira_hub.hub_user_cycle;
create table if not exists mira_hub.hub_user_cycle
(
    id                bigint auto_increment comment 'id'
        primary key,
    hub_id            varchar(32)  not null,
    cycle_index       int          not null,
    cycle_length      int          not null,
    date_period_start varchar(255) null,
    date_period_end   varchar(255) null,
    cycle_status      int          not null,
    modify_time       bigint       not null,
    sync_time         bigint       not null comment 'data synchronized time'
)
    comment 'hub user cycle analysis data';

create unique index idx_hub_cycle_id
    on hub_user_cycle (hub_id,cycle_index);

-- 9--------------
drop table if exists mira_hub.hub_user_daily_log;
create table if not exists mira_hub.hub_user_daily_log
(
    id                bigint auto_increment comment 'id'
        primary key,
    hub_id            varchar(32)                             not null,
    date_str          varchar(255)                            null comment '日记时间（年月日）',
    sex               varchar(4)                              null comment '是否同房',
    mucus_type        varchar(4)                              null comment '白带状态',
    mucus_flow        varchar(4)                              null comment '白带流量',
    pregnant          tinyint                                 null comment '验孕记录',
    opk               tinyint                                 null comment '排卵测试记录',
    weight_l          decimal(19, 3)                          null comment '体重l',
    notes             varchar(512) collate utf8mb4_unicode_ci null,
    flow_and_spotting varchar(25)                             null comment '出血状态',
    cervical_position varchar(255)                            null comment '子宫位置',
    cervical_firmness varchar(512)                            null comment '子宫硬度',
    cervical_openness varchar(512)                            null comment '子宫开合状态',
    modify_time       bigint                                  not null,
    sync_time         bigint                                  not null comment 'data synchronized time'
)
    comment 'hub user daily custom log';

create index idx_hub_id
    on hub_user_daily_log (hub_id);

-- 10--------------
drop table if exists mira_hub.hub_user_daily_medication;
create table if not exists mira_hub.hub_user_daily_medication
(
    id          bigint auto_increment comment 'id'
        primary key,
    hub_id      varchar(32)  not null,
    date_str    varchar(255) null comment 'date',
    medications text         not null comment 'use medications json Array',
    modify_time bigint       not null,
    sync_time   bigint       not null comment 'data synchronized time'
)
    comment 'hub user daily medication log';

create index idx_hub_id
    on hub_user_daily_medication (hub_id);

-- 11--------------
drop table if exists mira_hub.hub_user_daily_symptom;
create table if not exists mira_hub.hub_user_daily_symptom
(
    id          bigint auto_increment comment 'id'
        primary key,
    hub_id      varchar(32)   not null,
    date_str    varchar(255)  null comment 'date',
    symptoms    varchar(1024) not null comment 'symptom json Array',
    modify_time bigint        not null,
    sync_time   bigint        not null comment 'data synchronized time'
)
    comment 'hub user daily body symptom log';

create index idx_hub_id
    on hub_user_daily_symptom (hub_id);

-- 12--------------
drop table if exists mira_hub.hub_user_daily_mood;
create table if not exists mira_hub.hub_user_daily_mood
(
    id               bigint auto_increment comment 'id'
        primary key,
    hub_id           varchar(32)  not null,
    date_str         varchar(255) null comment 'date',
    mood             varchar(15)  null comment '心情',
    mood_interfering varchar(4)   null comment '心情干扰',
    feeling          varchar(24)  null comment 'feeling',
    sex_drive        varchar(8)   null comment '性欲',
    productivity     varchar(8)   null comment '生产力',
    cravings         varchar(8)   null comment '渴望',
    exercise         varchar(8)   null comment '锻炼',
    skin             varchar(15)  null comment '皮肤',
    looks            varchar(8)   null comment '外表',
    modify_time      bigint       not null,
    sync_time        bigint       not null comment 'data synchronized time'
)
    comment 'hub user daily body symptom log';

create index idx_hub_id
    on hub_user_daily_mood (hub_id);

-- end--------------