#!/bin/bash

IMAGE_NAME="mira-third-party"
CONTAINER_NAME="mira-third-party"
PORT=8083

echo "===== Building Maven project ====="
mvn clean package -DskipTests

echo "===== Building Docker image ====="
docker build -t $IMAGE_NAME .

# Check if container already exists and remove it
if [ "$(docker ps -aq -f name=$CONTAINER_NAME)" ]; then
    echo "===== Stopping and removing existing container ====="
    docker stop $CONTAINER_NAME
    docker rm $CONTAINER_NAME
fi

get_host_ip() {
    local iface
    iface=$(route -n get default 2>/dev/null | awk '/interface:/{print $2; exit}')

    if [[ -z "$iface" ]]; then
        iface="en0"
    fi

    local ip
    ip=$(ipconfig getifaddr "$iface" 2>/dev/null)

    if [[ -z "$ip" ]]; then
        ip=$(ifconfig "$iface" 2>/dev/null | awk '/inet /{print $2; exit}')
    fi

    echo "$ip"
}

# register ip
HOST_IP=$(get_host_ip)

echo "===== Running Docker container ====="
docker run -d \
    --name $CONTAINER_NAME \
    -p $PORT:$PORT \
    -e SPRING_CLOUD_NACOS_DISCOVERY_IP=$HOST_IP \
    $IMAGE_NAME