package com.mira.api.user.consts;

/**
 * <AUTHOR>
 */
public class RedisCacheKeyConst {
    /**
     * login
     */
    public static final String USER_LOGIN_LOCK = "login_lock:";
    public static final String USER_LOGIN_ERROR = "login_error:";
    public static final String USER_LOGIN_VERIFY_CODE = "user_login_verify_code:";
    public static final String USER_LOGIN_INFO = "user_login_info:";

    /**
     * custom log
     */
    public static final String USER_CUSTOM_LOG_CONFIG = "custom_log_config:";

    /**
     * email
     */
    public static final String EMAIL_USER_REGISTER = "email_register:";
    public static final String EMAIL_CHANGE_EMAIL = "email_change_email:";
    public static final String EMAIL_RESET_PW = "email_email_reset_pw:";

    /**
     * firebase
     */
    public static final String USER_FIREBASE_TOKEN = "firebase_token:";

    /**
     * partner
     */
    public static final String PARTNER_INVITE_INFO = "partner_invite:";
    public static final String PARTNER_EMAIL_RESET_PW = "partner_email_reset_pw:";
    public static final String PARTNER_EMAIL_CHANGE_EMAIL = "partner_email_reset_pw:";

    /**
     * algorithm
     */
    public static final String USER_ALGORITHM_RESULT = "algorithm_result:";
    public static final String SYS_TIPS_ALGORITHM_RESULT = "tips_algorithm_result:";
    public static final String MENOPAUSE_RESULT = "menopause_result:";

    /**
     * sn limit
     */
    public static final String SYS_SN_LIMIT = "sn_limit:";

    /**
     * report
     */
    public static final String USER_REPORT = "user_report:";

    /**
     * doctor
     */
    public static final String NURSE_EMAIL_HISTORY = "nurse_email_historys:";
    public static final String CLINIC_EMAIL_REGISTER = "clinic:email_register:";
    public static final String CLINIC_EMAIL_RESET_PW = "clinic_email_reset_pw:";

    /**
     * klaviyo
     */
    public static final String PREGNANCY_START = "klaviyo_pregnancy_start";

    /**
     * 重复通知
     */
    public static final String REPEAT_PUSH = "notification_repeat_push:";

    /**
     * 用户通知信息
     */
    public static final String USER_REMIND = "remind_info:";

    /**
     * 数据上传白名单
     */
    public static final String DATA_UPLOAD_WHITE = "DATA_UPLOAD_WHITE";

    /**
     * deviceId校验白名单
     */
    public static final String DEVICE_ID_WHITE = "DEVICE_ID_WHITE";

    /**
     * 登录验证码不重复生成有效期
     */
    public static final String VERIFY_CODE_REPEAT = "verify_code_repeat:";

    /**
     * 同一用户指定时间范围内编辑经期check
     */
    public static final String EDIT_PERIOD_IDEMPOTENT = "period_edit_lock:";
    public static final String EDIT_PERIOD_NOT_UPDATE_DB_IDEMPOTENT = "period_edit_no_db_lock:";

    /**
     * 用户当天测试的试剂类型
     */
    public static final String USER_NEW_HORMONE_WANDTYPE_DAY = "new_hormone_wandtype:";

    /**
     * 延长经期，标记用户和日期
     */
    public static final String EXTEND_PERIOD_MARK = "mira-user:extend_period:";

    /**
     * 用户完成首次实周期
     */
    public static final String USER_FIRST_REAL_CYCLE = "user_first_cycle_completed:";

    /**
     * 从通知进入无经期模式标记
     */
    public static final String NO_PERIOD_MARK_NOTIFICATION = "no_period_mark_notification:";

    /**
     * tips 次数限制
     */
    public static final String TIPS_LIMIT = "tips_limit:";

    /**
     * 调用算法时，缓存未更新前的algorithm cycle和更新后的algorithm cycle
     */
    public static final String CYCLE_DATA_BEFORE_UPDATE = "cycle_data_before_update:";
    public static final String CYCLE_DATA_AFTER_UPDATE = "cycle_data_after_update:";

    //------------------ 以下为backend 缓存key ------------------
    public static final String ADMIN_USER_TOKEN = "ADMIN_USER_TOKEN:";
    public static final String CHECK_TOKEN = "CHECK-TOKEN:";

    public static final String HOME_BANNER_GROUPUID = "SYS:HOME_BANNER:GROUPUID";

    public static final String MFR_NETWORK = "MFR_NETWORK:";
}
