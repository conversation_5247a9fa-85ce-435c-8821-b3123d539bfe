package com.mira.api.bluetooth.dto.cycle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@ApiModel("周期分析结果数据")
public class CycleAnalysisDTO {
    @ApiModelProperty("周期长度平均值")
    private Integer cycle_len;

    @ApiModelProperty("经期长度平均值")
    private Integer period_len;

    @ApiModelProperty("黄体期长度平均值")
    private Integer luteal_phases;

    @ApiModelProperty("月经结束到排卵长度平均值")
    private Integer folicular_phases;

    @ApiModelProperty("排卵日Index平均值")
    private Integer ovulation_estimate;

    @ApiModelProperty("1: <21 days - short; " +
            "0: 21—35 days - normal; " +
            "2: >35 days - long; " +
            "-1: NA")
    private Integer cycle_len_v2;

    @ApiModelProperty("1: Irregular; " +
            "0: Regular; " +
            "-1: NA")
    private Integer cycle_len_var_v2;

    @ApiModelProperty("0: Short; " +
            "1: Normal; " +
            "3: Long; " +
            "-1: NA")
    private Integer period_len_v2;

    @ApiModelProperty("黄体期长度平均值_v2")
    private Integer luteal_phases_v2;

    @ApiModelProperty("月经结束到排卵长度平均值_v2")
    private Integer folicular_phases_v2;

    @ApiModelProperty("排卵日Index平均值_v2")
    private Integer ovulation_estimate_v2;

    @ApiModelProperty("卵巢储备_v2: " +
            "1-Normal; " +
            "0-Low; " +
            "-1-NA")
    private Integer ovarian_reserve_v2;

    @ApiModelProperty("cycle_history_analysis")
    private List<HistoryAnalysis> cycle_history_analysis = new ArrayList<>();

    @Getter
    @Setter
    public static class HistoryAnalysis {
        @ApiModelProperty("Cycle Index")
        private Integer cycle_index;

        @ApiModelProperty("周期概述_v2: " +
                "-1-NA; " +
                "0-LH surge + PdG rise + E3G is within the range; " +
                "1-LH surge + PdG rise + E3G is outside the range; " +
                "2-No LH surge, but PdG rise; " +
                "3-LH surge, but no PdG rise; " +
                "4-no LH surge; no PdG rise, E3G is within range")
        private Integer cycle_overview_v2;

        @ApiModelProperty("e3g_max_v2")
        private Double e3g_max_v2;

        @ApiModelProperty("e3g_min_v2")
        private Double e3g_min_v2;

        @ApiModelProperty("e3g_folicular_phase_v2: " +
                "-1-NA; " +
                "0-Within the range; " +
                "1-Higher; " +
                "2-Lower")
        private Integer e3g_folicular_phase_v2;

        @ApiModelProperty("e3g_luteal_phases_v2: " +
                "-1-NA; " +
                "0-Within the range; " +
                "1-Higher; " +
                "2-Lower")
        private Integer e3g_luteal_phases_v2;

        @ApiModelProperty("e3g_no_period_v2: " +
                "-1-NA; " +
                "0-Within the range; " +
                "1-Higher; " +
                "2-Lower")
        private Double e3g_no_period_v2;

        @ApiModelProperty("pdg_max_v2")
        private Double pdg_max_v2;

        @ApiModelProperty("pdg_min_v2")
        private Double pdg_min_v2;

        @ApiModelProperty("pdg_rise_v2: " +
                "-1-NA; " +
                "0-Rise not detected; " +
                "1-Rise is detected")
        private Integer pdg_rise_v2;

        @ApiModelProperty("lh_max_v2")
        private Double lh_max_v2;

        @ApiModelProperty("lh_min_v2")
        private Double lh_min_v2;

        @ApiModelProperty("lh_surge_v2: " +
                "-1-NA; " +
                "0-LH Surge not detected; " +
                "1-Surge is detected; " +
                "2-Additional LH Pattern")
        private Integer lh_surge_v2;

        @ApiModelProperty("fsh_max_v2")
        private Double fsh_max_v2;

        @ApiModelProperty("fsh_min_v2")
        private Double fsh_min_v2;

        @ApiModelProperty("e3g_pdg_coordination_v2: " +
                "-1-NA; " +
                "0-Normal E3G, PdG rise; " +
                "1-Higher E3G, PdG rise; " +
                "2-Higher E3G, no PdG rise")
        private Integer e3g_pdg_coordination_v2;

        @ApiModelProperty("e3g_lh_coordination_v2: " +
                "-1-NA; " +
                "0-LH surge, no E3G rise; " +
                "1-LH surge, E3G rise")
        private Integer e3g_lh_coordination_v2;

        @ApiModelProperty("fsh_lh_ratio_coordination_v2: " +
                "-1: NA; " +
                "0: Good ratio; " +
                "1: Too high ratio")
        private Integer fsh_lh_ratio_coordination_v2;

        @ApiModelProperty("fsh_lh_ovu_coordination_v2: " +
                "0: Normal pattern: LH and FSH surge together or FSH rises 1 day before or after LH; " +
                "1: Wrong pattern: LH surge without FSH surge; " +
                "2: Wrong pattern: Ongoing elevated FSH; " +
                "3: Wrong pattern: FSH changes without an LH surge")
        private Integer fsh_lh_ovu_coordination_v2;
    }

}
