package com.mira.api.bluetooth.provider;

import com.mira.api.bluetooth.consts.DataManualApiConst;
import com.mira.api.bluetooth.dto.backend.*;
import com.mira.api.bluetooth.dto.hormone.ManualDataDTO;
import com.mira.api.bluetooth.dto.hormone.UserDataManualPageRequestDTO;
import com.mira.api.bluetooth.dto.hormone.UserManualDataResponseDTO;
import com.mira.core.response.CommonResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * 手动添加数据
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-08-14
 **/
@FeignClient(value = "mira-bluetooth", contextId = "data-manual")
public interface IDataManualProvider {
    /**
     * 手动添加测试数据分页列表（给后台管理系统提供）
     *
     * @param dataManualPageRequestDTO
     * @return ManualDataResponseDTO
     */
    @PostMapping(DataManualApiConst.MANUAL_DATA_PAGE)
    CommonResult<ManualDataResponseDTO> manualDataPage(@RequestBody DataManualPageRequestDTO dataManualPageRequestDTO);

    @PostMapping(DataManualApiConst.USER_MANUAL_DATA_PAGE)
    CommonResult<UserManualDataResponseDTO> userManualDataPage(@RequestBody UserDataManualPageRequestDTO userDataManualPageRequestDTO);


    @GetMapping(DataManualApiConst.DETAIL)
    CommonResult<DataManualDTO> detail(@RequestParam("id") Long id, @RequestParam("adminId") Long adminId);

    @PostMapping(DataManualApiConst.AUDIT)
    CommonResult<Integer> audit(@RequestBody DataManualAuditDTO dataManualAuditDTO, @RequestParam("adminId") Long adminId);

    @PostMapping(DataManualApiConst.WAND_BATCH_LIST)
    CommonResult<List<BackendWandsParamRecordDTO>> wandBatchList(@RequestParam("testWandType") String testWandType);

    @PostMapping(DataManualApiConst.ADD_TEST_DATA)
    CommonResult<Void> addTestData(@RequestBody List<ManualAddTestDataDTO> testDataParamList);

    @PostMapping(DataManualApiConst.IGNORE)
    CommonResult<Void> ignore(@RequestParam("id") Long id);

    @PostMapping(DataManualApiConst.SEND_NOTIFICATION)
    CommonResult<Void> sendNotification(@RequestParam("id") Long id, @RequestParam("notificationDefineId") Long notificationDefineId);

    @PostMapping(DataManualApiConst.ADD_HORMONE_DATA)
    CommonResult<Void> addHormoneData(@RequestBody ManualDataDTO manualDataDTO,
                                         @RequestParam("userId")Long userId,@RequestParam("timeZone")String timeZone);

    @PostMapping(DataManualApiConst.ADD_DELAYED_TASK_HORMONE_DATA)
    CommonResult<Void> addDelayedTaskHormoneData(@RequestParam("userId")Long userId, @RequestParam("testWandType")String testWandType,
                                                 @RequestParam("completeTime")String completeTime,@RequestParam("dataManualJson")String dataManualJson);

    @PostMapping(DataManualApiConst.ADD_UNPROCESS_MANUAL_DATA_2TASK)
    CommonResult<Integer> addUnprocessManualData2Task(@RequestParam("count")Integer count);
}
