package com.mira.api.clinic.enums;

import lombok.Getter;

/**
 * 病人状态枚举
 *
 * <AUTHOR>
 */
@Getter
public enum ClinicPatientStatusEnum {
    INVITING(1, "邀请中"),
    NORMAL(2, "正常激活状态"),
    DELETED(3, "用户账号已注销"),
    REGISTERING(4, "用户账号注册中"),
    REJECTION(5, "用户拒绝成为病人"),
    INVITATION_EXPIRED(6, "邀请链接已过期");

    private final Integer code;
    private final String desc;

    ClinicPatientStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ClinicPatientStatusEnum getByCode(Integer code) {
        for (ClinicPatientStatusEnum clinicPatientStatusEnum : ClinicPatientStatusEnum.values()) {
            if (clinicPatientStatusEnum.getCode().equals(code)) {
                return clinicPatientStatusEnum;
            }
        }
        return null;
    }
}
