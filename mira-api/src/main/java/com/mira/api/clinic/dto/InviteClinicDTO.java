package com.mira.api.clinic.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * <p>
 * 邀请诊所
 * </p>
 *
 * @author: xizhao.dai
 **/
@Data
public class InviteClinicDTO {
    @NotBlank(message = "clinic code can not be empty")
    @ApiModelProperty(value = "clinic code", required = true)
    private String clinicCode;
    @NotBlank(message = "clinic name can not be empty")
    @ApiModelProperty(value = "clinic 名称", required = true)
    @Size(max = 99, message = "Clinic name is too lengthy. Please use a shorter name.")
    private String clinicName;
    @ApiModelProperty(value = "clinic 类型：0:clinic;1:doctor; 默认0", required = true)
    private Integer clinicType;
    @NotBlank(message = "clinic icon can not be empty")
    @ApiModelProperty(value = "clinic 图标", required = true)
    private String icon;
    @NotBlank(message = "clinic notification Icon can not be empty")
    @ApiModelProperty(value = "clinic 接收app通知的图标", required = true)
    private String notificationIcon;
    @ApiModelProperty(value = "clinic websiteUrl", required = false)
    private String websiteUrl;
    @NotBlank(message = "clinic email can not be empty")
    @ApiModelProperty(value = "管理员email", required = true)
    private String email;

    @Override
    public String toString() {
        return "InviteClinicDTO{" +
                "clinicCode='" + clinicCode + '\'' +
                ", clinicName='" + clinicName + '\'' +
                ", clinicType=" + clinicType +
                ", icon='" + icon + '\'' +
                ", notificationIcon='" + notificationIcon + '\'' +
                ", websiteUrl='" + websiteUrl + '\'' +
                ", email='" + email + '\'' +
                '}';
    }
}
