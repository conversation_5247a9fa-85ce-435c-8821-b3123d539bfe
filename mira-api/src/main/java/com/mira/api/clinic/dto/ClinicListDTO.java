package com.mira.api.clinic.dto;

import com.mira.api.clinic.enums.ClinicianStatusEnum;
import lombok.Data;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-08-28
 **/
@Data
public class ClinicListDTO {
    /**
     * clinic id
     */
    private Long id;

    /**
     * 租户code
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户图标
     */
    private String icon;

    /**
     * 类型：0:clinic;1:doctor; 默认0
     */
    private Integer tenantType;

    /**
     * 通知图标
     */
    private String notificationIcon;

    /**
     * 初始密码
     */
    private String initPassword;

    /**
     * 租户描述
     */
    private String websiteUrl;

    /**
     * 创建时间
     */
    private String createTimeStr;

    /**
     * 修改时间
     */
    private String modifyTimeStr;

    private Long doctorId;

    private String email;

    /**
     * @see ClinicianStatusEnum
     */
    private Integer status;

    /**
     * 记录账户的邀请时间，仅对被邀请中和邀请过期，该字段有意义
     */
    private Long inviteTime;
}
