package com.mira.api.clinic.enums;

import lombok.Getter;

/**
 * 诊所管理员状态
 */
@Getter
public enum ClinicianStatusEnum {
    INVITING(1, "Invited"),
    CONFIRMED(2, "Account confirmed"),
    INVITATION_EXPIRED(6, "邀请链接已过期");

    private final Integer code;
    private final String desc;

    ClinicianStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
