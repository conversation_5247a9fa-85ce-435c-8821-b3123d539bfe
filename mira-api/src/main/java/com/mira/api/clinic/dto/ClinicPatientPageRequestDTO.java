package com.mira.api.clinic.dto;

import com.mira.core.request.PageDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@ApiModel("病人分页参数")
public class ClinicPatientPageRequestDTO extends PageDTO {
    @ApiModelProperty("搜索关键字")
    private String keyword;
    @ApiModelProperty(value = "clinic code", required = true)
    private String clinicCode;
}
