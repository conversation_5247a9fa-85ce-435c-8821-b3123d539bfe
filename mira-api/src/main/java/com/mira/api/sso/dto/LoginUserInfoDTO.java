package com.mira.api.sso.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 登录用户详细信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel("登录用户详细信息")
public class LoginUserInfoDTO {
    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("昵称")
    private String nickname;

    @ApiModelProperty("英文名")
    private String firstName;

    @ApiModelProperty("英文姓")
    private String lastName;

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("出生年份")
    private Integer birthYear;

    @ApiModelProperty("出生月份")
    private Integer birthMonth;

    @ApiModelProperty("出生日期")
    private Integer birthDay;

    @ApiModelProperty("用户目标，参考 UserGoalEnum")
    private Integer goalStatus;

    @ApiModelProperty("用来控制fsh的测试提醒，1表示有fsh测试日推荐，0代表无")
    private Integer isOft;

    @ApiModelProperty("绑定的固件版本")
    private String bindVersion;

    @ApiModelProperty("绑定的设备")
    private String bindDevice;

    @ApiModelProperty("Firebase 推送 Token")
    private String pushToken;

    @ApiModelProperty("平台，3-iOS；4-android")
    private Integer platform;

    @ApiModelProperty("是否跟踪更年期，null无标识;0 不跟踪;1跟踪")
    private Integer trackingMenopause;

    @ApiModelProperty("开始跟踪更年期（如果有退出，则去除这个时间，下次进入时再设置）")
    private String trackingMenopauseDate;

    @ApiModelProperty("用户 Condition，参考 UserConditionEnum")
    private String conditions;

    @ApiModelProperty("节育方式")
    private Integer hormonalBirthControl;

    @ApiModelProperty("firebase通知标识:0 no remind，1remind")
    private Integer remindFlag;

    @ApiModelProperty("通知时间")
    private Long remindTime;

    @ApiModelProperty("消息通知标识:0 no, 1 yes")
    private Integer testingScheduleFlag;

    @ApiModelProperty("临床测试的 product code 标识")
    private Integer productTrialFlag;

    @ApiModelProperty("模式切换的开关")
    private Integer goalTrialFlag;

    @ApiModelProperty("平均经期长度")
    private Integer avgLenPeriod;

    @ApiModelProperty("平均周期长度")
    private Integer avgLenCycle;

    @ApiModelProperty("经期标识，0：正常；-1：I don't know")
    private Integer periodFlag;

    @ApiModelProperty("周期标识，0：正常；-1：I don't know；-2 varies often；-3 I don't know + varies often")
    private Integer cycleFlag;

    @ApiModelProperty("tta开关，默认0关闭，1临床打开，2全部打开")
    private Integer ttaSwitch;

    @Deprecated
    @ApiModelProperty("是否新用户：0新用户，1老用户")
    private Integer fresh;

    @ApiModelProperty("Onboarding状态，0-未完成，1-到连接仪器，2-到达首页")
    private Integer onboardingStatus;

    @ApiModelProperty("Onboarding Page")
    private String onboardingPage;

    @ApiModelProperty("No Period 标记，0-Cycle Tracking，2-TTC")
    private Integer noPeriodFlag;

    @ApiModelProperty("不规则周期，0-否，1-是")
    private Integer irregularCycle;

    @ApiModelProperty("使用经期表中的时区")
    private String timeZone;

    @ApiModelProperty("计算得到的经期信息")
    private String periodData;

    @ApiModelProperty("是否是好用户：0-否，1-是")
    private Integer goodUser;

    @ApiModelProperty("新旧版本数据同步状态")
    private Integer transferFlag;

    @ApiModelProperty("国家编号")
    private String countryCode;

    @ApiModelProperty("洲编号")
    private String continentCode;

    @ApiModelProperty("当前货币")
    private String currentCurrency;

    @ApiModelProperty("partner邮箱")
    private String partnerEmail;

    @ApiModelProperty("partner状态：0-未激活，1-已完成激活但未注册，2-已完成注册")
    private String partnerStatus;

    @ApiModelProperty("是否是仪器黑名单：true是黑名单；false不是（仅针对bindDevice不为空的情况）")
    private Boolean blackSn;

    @ApiModelProperty("用户注册时间")
    private String registerTime;

    @ApiModelProperty("扩展字段、额外信息")
    private String extraInfo;
}
